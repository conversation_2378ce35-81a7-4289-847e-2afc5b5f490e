// repos/grnRepo.js

const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

// Constant reference to GRNs collection
const GRNS_COLLECTION = db.collection(COLLECTIONS.GRN);
const {
  DATE_FORMAT,
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const { paiseToRupee } = require("@/utils/money");
const { LedgerTypes } = require("@/defs/ledgerDefs");
const StockLedgerRepo = require("@/repositories/stockLedgerRepo");
const { StockTransactionType } = require("@/defs/ledgerDefs");
const { creditStock, debitStock } = require("@/services/stockService");

/**
 * Retrieves a single GRN document by its ID and tenant ID.
 * @param {string} grnId - Unique identifier of the GRN document to retrieve.
 * @param {string} tenantId - Tenant ID to filter GRNs by.
 * @param {firebase.firestore.Transaction} transaction - Optional transaction to use.
 * @returns {Promise<object>} - Retrieved GRN document or null if not found.
 */

const parseGRNData = (data, id) => {
  return {
    ...data,
    grnId: id,
    grnDate: FD.toFormattedDate(data.grnDate, DATE_FORMAT.DATE_ONLY),
    invoiceDate: FD.toFormattedDate(data.invoiceDate, DATE_FORMAT.DATE_ONLY),
    createdAt: FD.toFormattedDate(data.createdAt),
    removedAt: data.removedAt ? FD.toFormattedDate(data.removedAt) : null,
    updatedAt: FD.toFormattedDate(data.updatedAt),
    totalAmount: paiseToRupee(data.totalAmount),
    totalDiscount: paiseToRupee(data.totalDiscount),
    totalCess: paiseToRupee(data.totalCess),
    totalFocAmount: paiseToRupee(data.totalFocAmount),
    totalTaxAmount: paiseToRupee(data.totalTaxAmount),
    totalChargeAmount: paiseToRupee(data.totalChargeAmount),
    grossAmount: paiseToRupee(data.grossAmount),
    netAmount: paiseToRupee(data.netAmount),
    taxes: data.taxes || [],
    charges: data.charges || [],
    items: data.items.map((item) => ({
      ...item,
      orderedQty: item.orderedQty,
      receivedQty: item.receivedQty,
      pkg: item.pkg,
      uom: item.pkg?.id !== "default" ? item.pkg.name : item.purchaseUOM,
      unitCost: paiseToRupee(item.unitCost),
      totalAmount: paiseToRupee(item.totalAmount),
      totalTaxAmount: paiseToRupee(item.totalTaxAmount),
      totalDiscount: paiseToRupee(item.totalDiscount),
      totalCess: paiseToRupee(item.totalCess),
      grossAmount: paiseToRupee(item.grossAmount),
      totalChargeAmount: paiseToRupee(item.totalChargeAmount),
      totalFocAmount: paiseToRupee(item.totalFocAmount),
      netAmount:
        paiseToRupee(item.unitCost) * item.receivedQty -
        paiseToRupee(item.totalDiscount),
    })),
  };
};
async function getGRNById(grnId, tenantId, transaction, parse = true) {
  const ref = GRNS_COLLECTION.doc(grnId);
  const doc = transaction ? await transaction.get(ref) : await ref.get();
  if (!doc.exists) return null;
  const data = doc.data();

  if (data.tenantId !== tenantId) return null;
  if (!parse) return data;
  return parseGRNData(data, doc.id);
}

async function convertData(data, id) {
  return {
    ...data,
    grnId: id,
    grnDate: data.grnDate,
    invoiceDate: data.invoiceDate,
    createdAt: data.createdAt,
    removedAt: data.removedAt ? data.removedAt : null,
    updatedAt: data.updatedAt,
    totalAmount: data.totalAmount,
    totalDiscount: paiseToRupee(data.totalDiscount),
    totalCess: paiseToRupee(data.totalCess),
    totalFocAmount: paiseToRupee(data.totalFocAmount),
    totalTaxAmount: paiseToRupee(data.totalTaxAmount),
    totalChargeAmount: paiseToRupee(data.totalChargeAmount),
    grossAmount: data.grossAmount,
    netAmount: data.netAmount,
    taxes: data.taxes || [],
    charges: data.charges || [],
    items: data.items.map((item) => ({
      ...item,
      orderedQty: item.orderedQty,
      receivedQty: item.receivedQty,
      pkg: item.pkg,
      uom: item.pkg?.id !== "default" ? item.pkg.name : item.purchaseUOM,
      unitCost: paiseToRupee(item.unitCost),
      totalAmount: paiseToRupee(item.totalAmount),
      totalTaxAmount: paiseToRupee(item.totalTaxAmount),
      totalDiscount: paiseToRupee(item.totalDiscount),
      totalCess: paiseToRupee(item.totalCess),
      grossAmount: paiseToRupee(item.grossAmount),
      totalChargeAmount: paiseToRupee(item.totalChargeAmount),
      totalFocAmount: paiseToRupee(item.totalFocAmount),
      netAmount:
        paiseToRupee(item.unitCost) * item.receivedQty -
        paiseToRupee(item.totalDiscount),
    })),
  };
}

async function getGRNByNumber(tenantId, grnNumber) {
  let query = GRNS_COLLECTION.where("tenantId", "==", tenantId)
    .where("grnNumber", "==", grnNumber)
    .limit(1);

  const snapshot = await query.get();
  if (snapshot.empty) return null;
  return snapshot.docs[0].data();
}

/**
 * Lists GRN documents based on the given filters.
 * - Filters GRNs by tenantId.
 * - Optionally filters GRNs by locationId, startDate, and endDate.
 * - The startDate and endDate filters are inclusive.
 * @param {object} filters - Object containing filters to apply.
 * @param {string} filters.tenantId - Required. Tenant ID to filter GRNs by.
 * @param {string} [filters.locations] - Optional. Location ID to filter GRNs by.
 * @param {string} [filters.inventoryLocations] - Optional. Inventory Location ID to filter GRNs by.
 * @param {string} [filters.fromDate] - Optional. Start date to filter GRNs by.
 * @param {string} [filters.toDate] - Optional. End date to filter GRNs by.
 * @returns {Promise<Array<object>>} - List of GRN documents or empty array if no results are found.
 */
// async function listGRNs({
//   tenantId,
//   locations,
//   inventoryLocations,
//   vendors,
//   fromDate,
//   toDate,
//   attachments,
//   grnStatus,
// }) {
//   let query = GRNS_COLLECTION.where("tenantId", "==", tenantId);

//   if (locations?.length) query = query.where("location.id", "in", locations);
//   if (inventoryLocations?.length)
//     query = query.where("inventoryLocation.id", "in", inventoryLocations);

//   if (vendors?.length) {
//     query = query.where("vendor.id", "in", vendors);
//   }

//   if (fromDate) {
//     query = query.where(
//       "updatedAt",
//       ">=",
//       FD.toFirestore(fromDate, TIME_OPTION.START)
//     );
//   }
//   if (toDate) {
//     query = query.where(
//       "updatedAt",
//       "<=",
//       FD.toFirestore(toDate, TIME_OPTION.END)
//     );
//   }

//   if (attachments === "noAttachments") {
//     // Only documents with NO attachments
//     query = query.where("attachments", "==", null);
//   } else if (attachments === "attachments") {
//     // Only documents WITH attachments
//     query = query.where("attachments", "!=", null);
//   }

//   const statusMap = {
//     complete: "completed",
//     delete: "deleted",
//     rtv: "returnVendor",
//   };

//   if (grnStatus && grnStatus !== "all") {
//     const status = statusMap[grnStatus];
//     query = query.where("status", "==", status);
//   }

//   const snapshot = await query.get();
//   return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
// }

async function listGRNs(filters) {
  const raw = await fetchAllGRNs(filters);
  return applyGRNFilters(raw, filters);
}

async function fetchAllGRNs({ tenantId, fromDate, toDate }) {
  let query = GRNS_COLLECTION.where("tenantId", "==", tenantId);

  if (fromDate) {
    query = query.where(
      "updatedAt",
      ">=",
      FD.toFirestore(fromDate, TIME_OPTION.START)
    );
  }

  if (toDate) {
    query = query.where(
      "updatedAt",
      "<=",
      FD.toFirestore(toDate, TIME_OPTION.END)
    );
  }

  const snapshot = await query.get();
  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

function applyGRNFilters(
  items,
  {
    locations = [],
    inventoryLocations = [],
    vendors = [],
    attachments,
    grnStatus,
  }
) {
  const statusMap = {
    complete: "completed",
    delete: "deleted",
    rtv: "returnVendor",
  };

  const wantedStatus =
    grnStatus && grnStatus !== "all" ? statusMap[grnStatus] : null;

  return items.filter((item) => {
    if (locations?.length && !locations.includes(item.location?.id))
      return false;

    if (
      inventoryLocations?.length &&
      !inventoryLocations.includes(item.inventoryLocation?.id)
    )
      return false;

    if (vendors?.length && !vendors.includes(item.vendor?.id)) return false;

    if (attachments === "noAttachments" && item.attachments != null)
      return false;
    if (attachments === "attachments" && item.attachments == null) return false;

    if (wantedStatus && item.status !== wantedStatus) return false;

    return true;
  });
}

/**
 * Creates a new GRN document with the given data
 * @param {string} grnId - ID of the GRN document to create
 * @param {object} grnData - GRN data to be created
 * @param {firebase.firestore.Transaction} transaction - Optional transaction to use
 * @returns {Promise<object>} - Newly created GRN document
 */
async function createGRN(grnId, grnData, transaction) {
  const ref = GRNS_COLLECTION.doc(grnId);
  const payload = {
    ...grnData,
    tenantId: grnData.tenantId,
    createdAt: FD.now(),
    updatedAt: FD.now(),
  };

  if (transaction) {
    await transaction.set(ref, payload);
  } else {
    await ref.set(payload);
  }

  return { grnId, ...payload };
}

async function updateGRNDocument(id, oldGrn, newGrn, diffs, t) {
  const ref = GRNS_COLLECTION.doc(id);

  t.update(ref, {
    ...newGrn,
    updatedAt: FD.now(),
    editHistory: [
      ...(oldGrn.editHistory || []),
      {
        timestamp: FD.now(),
        editedById: newGrn.editedById,
        editedByName: newGrn.editedByName,
        changes: diffs.map((c) => ({
          field: c.field,
          old: c.oldValue,
          new: c.newValue,
        })),
      },
    ],
  });

  return parseGRNData(newGrn, id);
}

const updateAttachments = async (id, attachments) => {
  const ref = GRNS_COLLECTION.doc(id);
  const doc = await ref.get();

  if (!doc.exists) {
    throw new Error("Grn not found");
  }

  const existingAttachments = doc.data().attachments || [];

  const updatedAttachments = [
    ...existingAttachments.filter(
      (f) =>
        !attachments.some(
          (s) => s.filePath === f.filePath || s.fileName === f.fileName
        )
    ),
    ...attachments,
  ];

  await ref.update({ attachments: updatedAttachments });

  return updatedAttachments;
};

const deleteAttachment = async (id, filePath) => {
  const ref = GRNS_COLLECTION.doc(id);
  const doc = await ref.get();

  if (!doc.exists) throw new Error("Grn not found");

  const attachments = doc.data().attachments || [];
  const updatedAttachments = attachments.filter((f) => f.filePath !== filePath);

  await ref.update({
    attachments: updatedAttachments.length > 0 ? updatedAttachments : null,
  });

  return updatedAttachments;
};

const consumeOriginalLedgers = async ({
  grnId,
  tenantId,
  itemId,
  pkgId,
  qty,
  t,
}) => {
  if (!qty) return;

  const pkgSafe = pkgId || "default";

  // Fetch only IN entries created by this GRN
  const snap = await db
    .collection(COLLECTIONS.LEDGERS)
    .where("tenantId", "==", tenantId)
    .where("ledgerType", "==", LedgerTypes.GRN)
    .where("transactionType", "==", StockTransactionType.IN)
    .where("grnMeta.id", "==", grnId)
    .where("itemId", "==", itemId)
    .where("pkg.id", "==", pkgSafe)
    .orderBy("createdAt", "desc")
    .get();

  if (snap.empty) {
    throw new Error(`No IN ledger entries found for item ${itemId}`);
  }

  let remaining = qty;

  for (const doc of snap.docs) {
    if (remaining <= 0) break;

    const data = doc.data();
    const remQty = Number(data.remainingQty || 0);
    if (remQty <= 0) continue;

    const take = Math.min(remQty, remaining);

    t.update(doc.ref, {
      remainingQty: remQty - take,
      updatedAt: FD.now(),
    });

    remaining -= take;
  }

  if (remaining > 0) {
    throw new Error(
      `Insufficient remainingQty in GRN ledgers for item ${itemId}`
    );
  }
};

const baseLedgerPayload = ({
  tenantId,
  item,
  uom,
  oldGrn,
  action,
  ledgerType,
  transactionType,
}) => {
  return {
    tenantId,
    itemId: item.itemId,
    itemCode: item.itemCode,
    itemName: item.itemName,
    ledgerType,
    transactionType,
    categoryId: item.categoryId,
    categoryName: item.categoryName,
    subcategoryId: item.subcategoryId,
    subcategoryName: item.subcategoryName,
    qty: action.qty,
    countingUOM: uom,
    pkg: item.pkg,
    unitCost: item.unitCost,
    totalCost: item.totalAmount,
    remarks: item.remarks,
    locationId: oldGrn.location.id,
    locationName: oldGrn.location.name,
    inventoryLocationId: oldGrn.inventoryLocation.id,
    inventoryLocationName: oldGrn.inventoryLocation.name,
    grnMeta: {
      id: oldGrn.id,
      grnNumber: oldGrn.grnNumber,
      edit: true,
      reason: action.reason || "COST_OR_TAX_CHANGE",
    },
    createdAt: FD.now(),
    updatedAt: FD.now(),
  };
};

const ledgerNotePayload = (params) => {
  const base = baseLedgerPayload({
    ...params,
    ledgerType: LedgerTypes.GRN_EDIT_NOTE,
    transactionType: "NOTE",
  });

  return {
    ...base,
    discount: params.item.totalDiscount,
    taxRate: params.item.taxRate,
    taxAmount: params.item.totalTaxAmount,
    foc: params.item.foc,
    cess: params.item.totalCess,
    note: {
      field: params.action.field,
      oldValue: params.action.oldValue,
      newValue: params.action.newValue,
    },
  };
};

const applyStockActions = async (actions, oldGrn, newGrn, t) => {
  const tenantId = oldGrn.tenantId;
  const invLocId = oldGrn.inventoryLocation.id;

  for (const action of actions) {
    const item = action.item;

    const uom =
      item.pkg && item.pkg.id !== "default" ? item.pkg.name : item.purchaseUOM;

    // 1) STOCK OUT (decrease)
    if (action.action === "STOCK_OUT") {
      // First consume from original GRN IN-ledgers
      await debitStock({
        ledgerType: LedgerTypes.GRN_EDIT_DECREASE,
        tenantId,
        locationId: oldGrn.location.id,
        locationName: oldGrn.location.name,
        inventoryLocationId: invLocId,
        inventoryLocationName: oldGrn.inventoryLocation.name,
        itemId: item.itemId,
        itemCode: item.itemCode,
        itemName: item.itemName,
        qty: action.qty,
        pkgUOM: uom,
        unitCost: item.unitCost,
        totalCost: item.totalAmount,
        grnMeta: null,
        pkg: item.pkg,
        remarks: item.remarks || null,
        eventDate: newGrn.grnDate,
      });
    }

    //
    // 2) STOCK IN (increase)
    //
    if (action.action === "STOCK_IN") {
      await creditStock(
        {
          ledgerType: LedgerTypes.GRN_EDIT_INCREASE,
          tenantId,
          locationId: oldGrn.location.id,
          locationName: oldGrn.location.name,
          inventoryLocationId: invLocId,
          inventoryLocationName: oldGrn.inventoryLocation.name,
          itemId: item.itemId,
          itemCode: item.itemCode,
          itemName: item.itemName,
          categoryId: item.categoryId,
          categoryName: item.categoryName,
          subcategoryId: item.subcategoryId,
          subcategoryName: item.subcategoryName,
          orderedQty: item.orderedQty,
          qty: action.qty,
          pkgUOM: uom,
          unitCost: item.unitCost,
          totalCost: item.totalAmount,
          expiryDate: item.expiryDate,
          discount: item.totalDiscount,
          taxRate: item.taxRate,
          taxAmount: item.totalTaxAmount,
          pkg: item.pkg,
          remarks: item.remarks,
          foc: item.foc,
          cess: item.totalCess,
          grnMeta: {
            id: oldGrn.id,
            grnNumber: oldGrn.grnNumber,
            edit: true,
            reason: action.reason || "COST_OR_TAX_CHANGE",
          },
        },
        t
      );
    }

    //
    // 3) LEDGER NOTES (no stock movement)
    //
    if (action.action === "LEDGER_NOTE") {
      const notePayload = ledgerNotePayload({
        tenantId,
        item,
        uom,
        oldGrn,
        action,
      });

      await StockLedgerRepo.addEntry(notePayload, t);
    }
  }
};

const validateInvoiceNumber = async ({ vendorId, poId, invoiceNumber, t }) => {
  if (!invoiceNumber || typeof invoiceNumber !== "string") {
    throw new Error(`Invoice number is required`);
  }

  // Normalize incoming invoice number (case-insensitive logic)
  const normalizedIncoming = invoiceNumber.trim().toLowerCase();

  // Fetch all GRNs for this vendor except the current PO
  const grnSnap = await GRNS_COLLECTION.where("vendorId", "==", vendorId)
    .where("poId", "!=", poId)
    .get({ transaction: t });

  for (const doc of grnSnap.docs) {
    const data = doc.data();
    if (!data?.invoiceNumber) continue;

    const normalizedExisting = String(data.invoiceNumber).trim().toLowerCase();

    // Case-insensitive comparison
    if (normalizedExisting === normalizedIncoming) {
      throw new Error(`Invoice number already exists`);
    }
  }
};

module.exports = {
  getGRNById,
  listGRNs,
  createGRN,
  getGRNByNumber,
  updateAttachments,
  deleteAttachment,
  updateGRNDocument,
  applyStockActions,
  validateInvoiceNumber,
  convertData,
};
