const admin = require("firebase-admin");
const db = admin.firestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");
const { FirestoreDateHelper: FD, TIME_OPTION } = require("@/helpers/dateHelper");

const CONSUMPTION_TRACKING_COLLECTION = COLLECTIONS.CONSUMPTION_TRACKING;

/**
 * Save consumption tracking record to Firestore
 * @param {Object} data - Consumption tracking data
 * @param {firebase.firestore.Transaction} [transaction] - Optional transaction
 * @returns {Promise<Object>} Saved consumption tracking data with ID
 */
const saveConsumptionTracking = async (data, transaction = null) => {
  const docRef = db.collection(CONSUMPTION_TRACKING_COLLECTION).doc();
  const consumptionTrackingData = {
    ...data,
    id: docRef.id,
    createdAt: data.createdAt || FD.now(),
    updatedAt: FD.now(),
  };

  if (transaction) {
    await transaction.set(docRef, consumptionTrackingData);
  } else {
    await docRef.set(consumptionTrackingData);
  }

  return consumptionTrackingData;
};

/**
 * Get all consumption tracking records with filters
 * @param {Object} filters - Filter criteria
 * @returns {Promise<Array>} Array of consumption tracking records
 */
const getAllConsumptionTrackings = async (filters) => {
  let query = db.collection(CONSUMPTION_TRACKING_COLLECTION)
    .where("tenantId", "==", filters.tenantId);

  if (filters.locations?.length) {
    query = query.where("locationId", "in", filters.locations);
  }
  if (filters.inventoryLocations?.length) {
    query = query.where("inventoryLocationId", "in", filters.inventoryLocations);
  }
  if (filters.fromDate) {
    query = query.where(
      "consumptionDate",
      ">=",
      FD.toFirestore(filters.fromDate, TIME_OPTION.START)
    );
  }
  if (filters.toDate) {
    query = query.where(
      "consumptionDate",
      "<=",
      FD.toFirestore(filters.toDate, TIME_OPTION.END)
    );
  }
  if (filters.status) {
    query = query.where("status", "==", filters.status);
  }
  if (filters.consumptionType) {
    query = query.where("consumptionType", "==", filters.consumptionType);
  }

  const snapshot = await query.orderBy("consumptionDate", "desc").get();
  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};

/**
 * Get consumption tracking by ID
 * @param {string} id - Document ID
 * @returns {Promise<Object|null>} Consumption tracking data or null if not found
 */
const getById = async (id) => {
  const doc = await db.collection(CONSUMPTION_TRACKING_COLLECTION).doc(id).get();
  if (!doc.exists) {
    return null;
  }
  return { id: doc.id, ...doc.data() };
};

/**
 * Update consumption tracking by ID
 * @param {string} id - Document ID
 * @param {Object} data - Updated data
 * @param {firebase.firestore.Transaction} [transaction] - Optional transaction
 * @returns {Promise<void>}
 */
const updateById = async (id, data, transaction = null) => {
  const docRef = db.collection(CONSUMPTION_TRACKING_COLLECTION).doc(id);
  const updateData = {
    ...data,
    updatedAt: FD.now(),
  };

  if (transaction) {
    await transaction.update(docRef, updateData);
  } else {
    await docRef.update(updateData);
  }
};

/**
 * Delete consumption tracking by ID
 * @param {string} id - Document ID
 * @param {firebase.firestore.Transaction} [transaction] - Optional transaction
 * @returns {Promise<void>}
 */
const deleteById = async (id, transaction = null) => {
  const docRef = db.collection(CONSUMPTION_TRACKING_COLLECTION).doc(id);

  if (transaction) {
    await transaction.delete(docRef);
  } else {
    await docRef.delete();
  }
};

/**
 * Get consumption tracking records by date range
 * @param {string} tenantId - Tenant ID
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @returns {Promise<Array>} Array of consumption tracking records
 */
const getByDateRange = async (tenantId, startDate, endDate) => {
  const query = db.collection(CONSUMPTION_TRACKING_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("consumptionDate", ">=", FD.toFirestore(startDate, TIME_OPTION.START))
    .where("consumptionDate", "<=", FD.toFirestore(endDate, TIME_OPTION.END))
    .orderBy("consumptionDate", "desc");

  const snapshot = await query.get();
  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};

module.exports = {
  saveConsumptionTracking,
  getAllConsumptionTrackings,
  getById,
  updateById,
  deleteById,
  getByDateRange,
};
