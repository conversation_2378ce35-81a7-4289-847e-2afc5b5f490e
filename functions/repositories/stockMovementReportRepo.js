const { COLLECTIONS } = require("@/defs/collectionDefs");
const QueryBuilder = require("@/helpers/queryBuilder");
const { FirestoreDateHelper: FD, DEFAULT_TZ } = require("@/helpers/dateHelper");
const admin = require("firebase-admin");
const db = admin.firestore();
const dayjs = require("dayjs");
const axios = require("axios");
const BO_URL =
  "https://staging-bo-api.digitory.com/api/v1/inventory/accounts/63/department/summary";
const { getStores } = require("@/repositories/storeRepo");
const {
  getTransferInLedgersForReport,
} = require("@/repositories/stockLedgerRepo");

/**
 * Fetches report groups data with flexible filters.
 *
 * @param {string} tenantId - Tenant identifier (mandatory).
 * @param {object} filters - Filter options for date range, location, vendor, etc.
 * @returns {Promise<FirebaseFirestore.QuerySnapshot>} - Firestore query snapshot.
 */
const fetchReportGroupsData = async (tenantId, filters = {}) => {
  if (!tenantId) return [];

  const {
    reportDateType = "createdAt",
    _fsFromDate,
    _fsToDate,
    locations = [],
  } = filters;

  const [fromDate, toDate] = generateDateRange(_fsFromDate, _fsToDate, false);

  // get sales data
  const departments = await fetchSalesData(
    tenantId,
    locations,
    fromDate,
    toDate
  );

  // get report groups data
  const groupsSnap = await new QueryBuilder(COLLECTIONS.REPORT_GROUPS)
    .whereMust("tenantId", "==", tenantId)
    .query.get();

  if (groupsSnap.empty) return [];

  // get TRANSFER_IN ledgers data
  const ledgers = await getTransferInLedgersForReport({
    tenantId,
    dateField: reportDateType,
    fromDate: _fsFromDate,
    toDate: _fsToDate,
    locationIds: locations,
  });

  const categoryCostMap = ledgers.reduce((acc, l) => {
    if (!l.categoryId) return acc;

    acc[l.categoryId] = (acc[l.categoryId] || 0) + (l.totalCost || 0);

    return acc;
  }, {});

  return groupsSnap.docs.map((doc) => {
    const group = doc.data();
    const deptSales = departments[group.name?.toUpperCase()] || {};

    return {
      id: doc.id,
      ...group,
      grossAmount: deptSales.grossAmount || 0,
      netSales: deptSales.netSales || 0,
      category: (group.category || []).map((cat) => ({
        ...cat,
        cost: categoryCostMap[cat.id] || 0,
      })),
    };
  });
};

/**
 * Fetch sales data by posIds, startDate and endDate
 */
const fetchSalesData = async (tenantId, locations, fromDate, toDate) => {
  const stores = await getStores(tenantId, locations);

  const posIds = stores
    .map((s) => Number(s.posId))
    .filter((id) => Number.isInteger(id));

  if (!posIds.length) return {};

  const { data: salesData = {} } = await axios.put(
    BO_URL,
    {
      store_ids: posIds,
      from_date: fromDate,
      to_date: toDate,
    },
    {
      headers: { "Content-Type": "application/json" },
    }
  );

  return salesData.departments || {};
};

/**
 * Fetches transfer report data with flexible filters.
 *
 * @param {string} tenantId - Tenant identifier (mandatory).
 * @param {object} filters - Filter options for date range, location, vendor, etc.
 * @returns {Promise<FirebaseFirestore.QuerySnapshot>} - Firestore query snapshot.
 */
const fetchTransferReportData = async (tenantId, filters = {}) => {
  const dateField = filters.reportDateType || "requestedBy.time";
  const stockType = filters?.stockType;

  // Initialize QueryBuilder for transfers collection
  const q = new QueryBuilder(COLLECTIONS.TRANSFERS)
    .whereMust("tenantId", "==", tenantId)
    .whereIf(dateField, ">=", filters._fsFromDate)
    .whereIf(dateField, "<=", filters._fsToDate)
    .whereIf("requester.id", "in", filters.inventoryLocations);

  if (stockType === 2) {
    q.whereMust("stockableItems", "==", true);
  } else if (stockType === 3) {
    q.whereMust("stockableItems", "==", false);
  }

  // .whereIf("vendorId", "in", filters.vendors);

  //@todo: add select columns if needed

  // Execute query and return snapshot
  return await q.query.get();
};

const fetchTenantsReportData = async (tenantId, filters = {}) => {
  const dateRange = generateDateRange(filters._fsFromDate, filters._fsToDate);

  // 1. Resolve Locations
  let locations = filters.locations?.length ? filters.locations : [];
  if (locations.length === 0) {
    const locationsRef = db
      .collection("tenants")
      .doc(tenantId)
      .collection("locations");
    const documentRefs = await locationsRef.listDocuments();
    locations = documentRefs.map((doc) => doc.id);
  }

  // 2. Resolve Work Areas for each location
  // We use a Map to keep track of which WorkAreas belong to which Location
  let workAreasMap = {};
  const workAreaPromises = locations.map(async (locId) => {
    const waIds = filters.inventoryLocations?.length
      ? filters.inventoryLocations
      : [];

    if (waIds.length === 0) {
      const waRef = db
        .collection("tenants")
        .doc(tenantId)
        .collection("locations")
        .doc(locId)
        .collection("workAreas");

      const waDocs = await waRef.listDocuments();
      workAreasMap[locId] = waDocs.map((doc) => doc.id);
    } else {
      workAreasMap[locId] = waIds;
    }
  });

  await Promise.all(workAreaPromises);

  // 3. Prepare promises for the "items" subcollection
  // Structure: stockMovements > {date} > items > {id_pkgId}
  const promises = [];

  for (const locId of locations) {
    for (const waId of workAreasMap[locId]) {
      for (const date of dateRange) {
        const itemsRef = db
          .collection("tenants")
          .doc(tenantId)
          .collection("locations")
          .doc(locId)
          .collection("workAreas")
          .doc(waId)
          .collection("stockMovements")
          .doc(date)
          .collection("items");

        // Push the get() promise to the array
        promises.push(itemsRef.get());
      }
    }
  }

  // 4. Fetch all data and flatten results
  const snapshots = await Promise.all(promises);

  // Extract data from snapshots and apply secondary filters (categories, vendors, etc.)
  const reportData = snapshots.flatMap((snap) => {
    return snap.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        // Helpful for debugging or grouping later
        _locationId: doc.ref.parent.parent.parent.parent.parent.parent.id,
        _workAreaId: doc.ref.parent.parent.parent.parent.id,
        _date: doc.ref.parent.parent.id,
      };
    });
  });

  // 5. Apply JS-side filters for categories, vendors, etc.
  const result = reportData.filter((item) => {
    const matchCategory = filters.categories?.length
      ? filters.categories.includes(item.categoryId)
      : true;
    const matchSubCategory = filters.subCategories?.length
      ? filters.subCategories.includes(item.subCategoryId)
      : true;
    const matchItem = filters.inventoryItems?.length
      ? filters.inventoryItems.includes(item.inventoryItemId)
      : true;

    return matchCategory && matchSubCategory && matchItem;
  });

  // console.log(result, "result");
  return result;
};

/**
 * Generate date range from Firestore timestamps
 * Returns array of date strings in format 'DD-MMM-YYYY' (e.g., '30-Nov-2026')
 */
function generateDateRange(fromTimestamp, toTimestamp, dateRange = true) {
  const dates = [];

  if (!fromTimestamp || !toTimestamp) return dates;

  // Convert Firestore Timestamp → dayjs (IST)
  let startDate = dayjs(FD.toJSDate(fromTimestamp))
    .tz(DEFAULT_TZ)
    .startOf("day");

  const endDate = dayjs(FD.toJSDate(toTimestamp)).tz(DEFAULT_TZ).startOf("day");

  // if dateRange is false, return only start & end
  if (!dateRange) {
    return [startDate.format("DD-MMM-YYYY"), endDate.format("DD-MMM-YYYY")];
  }

  // if dateRange is true, returns start - end
  let currentDate = startDate;

  while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, "day")) {
    dates.push(currentDate.format("DD-MMM-YYYY"));
    currentDate = currentDate.add(1, "day");
  }
  return dates;
}

module.exports = {
  fetchTransferReportData,
  fetchTenantsReportData,
  fetchReportGroupsData,
};
