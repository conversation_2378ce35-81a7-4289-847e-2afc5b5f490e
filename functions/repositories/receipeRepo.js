// repositories/receipeRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

const RECEIPE_COLLECTION = db.collection(COLLECTIONS.RECEIPES);
const INVENTORY_LOCATIONS_COLLECTION = db.collection(COLLECTIONS.TENANT_INVENTORY_LOCATIONS);
const MENU_ITEMS_COLLECTION = db.collection(COLLECTIONS.MENU_ITEMS);

/**
 * Get a receipe by doc ID and validate tenant
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @returns {object|null} receipe data or null if not found / tenant mismatch
 */
async function getReceipeById(tenantId, docId) {
    const docRef = RECEIPE_COLLECTION.doc(docId);
    const snapshot = await docRef.get();

    if (!snapshot.exists) return null;

    const data = snapshot.data();
    if (data.tenantId !== tenantId) return null; // tenant validation

    return { id: snapshot.id, ...data };
}
/**
 * Get all receipe by floor ID and validate tenant
 * @param {string} tenantId - Tenant ID
 * @param {string} floorId - Floor ID
 * @returns {object|null} receipe data or null if not found / tenant mismatch
 */
async function getRecordsByFloorId(tenantId, floorId) {
    const snapshot = await INVENTORY_LOCATIONS_COLLECTION
      .where('tenantId', '==', tenantId)
      .where('floorIds', 'array-contains', floorId)
      .where('activeStatus', '==', true)
      .get();
  
    if (snapshot.empty) return [];
  
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

async function getMenuItemById(tenantId, menuItemId) {
    const snapshot = await MENU_ITEMS_COLLECTION
    .where('tenantId', '==', tenantId)
    .where('itemCode', '==', menuItemId)
    .limit(1)
    .get();
    if (snapshot.empty) return null;
    const doc = snapshot.docs[0];
    return { id: doc.id, ...doc.data() };
}

/**
 * Update activeStatus safely using transaction
 * Validates tenant before updating without fetching the full document
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @param {boolean} activeStatus - true = activate, false = deactivate
 */
async function updateReceipeStatus(tenantId, docId, activeStatus) {
    const docRef = RECEIPE_COLLECTION.doc(docId);

    await db.runTransaction(async (t) => {
        const snapshot = await t.get(docRef);
        if (!snapshot.exists) throw new Error("Receipe not found");

        const data = snapshot.data();
        if (data.tenantId !== tenantId) throw new Error("Unauthorized");

        t.update(docRef, { activeStatus });
    });
}

module.exports = {
    getReceipeById,
    updateReceipeStatus,
    getRecordsByFloorId,
    getMenuItemById
};
