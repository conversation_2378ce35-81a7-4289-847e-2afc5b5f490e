const ExcelJS = require("exceljs");
const { getHeaders, expandSheetNames } = require("./sheet");
const { saveData } = require("./saveData");

/**
 * Validate all required headers are present and match order (case-insensitive)
 * @param {string[]} headers - The headers from the sheet
 * @param {string[]} requiredHeaders - The expected headers
 * @returns {Array} Array of header errors, empty if no errors
 */
const validateHeaders = (headers, requiredHeaders) => {
  const errors = [];

  if (headers.length !== requiredHeaders.length) {
    errors.push({
      type: "length_mismatch",
      expected: requiredHeaders.length,
      got: headers.length,
    });
  }

  requiredHeaders.forEach((reqHeader, i) => {
    if (
      headers[i] &&
      headers[i].toString().toLowerCase() !==
        reqHeader.header.toString().toLowerCase()
    ) {
      errors.push({
        type: "header_mismatch",
        position: i + 1,
        expected: reqHeader,
        got: headers[i],
      });
    }
  });

  return errors;
};

/**
 * Validate workbook sheets and headers
 * @param {ExcelJS.Workbook} workbookSheets - Loaded Excel workbook
 * @param {string[]} formattedSheets - Sheets to validate
 * @returns {Object} { err: boolean, missingSheets: [], invalidHeaders: [] }
 */
const validateWorkBook = async (workbookSheets, formattedSheets) => {
  const report = {
    err: false,
    missingSheets: [],
    invalidHeaders: [],
  };

  for (const sheetName of formattedSheets) {
    const targetSheetNames = expandSheetNames(sheetName);

    for (const target of targetSheetNames) {
      const sheet = workbookSheets.getWorksheet(target);
      if (!sheet) {
        report.missingSheets.push(target);
        report.err = true;
        //return report;
        continue;
      }

      const headers = sheet.getRow(1).values.slice(1);
      const requiredHeaders = getHeaders(target) || [];
      const headerErrors = validateHeaders(headers, requiredHeaders);
      if (headerErrors.length > 0) {
        report.invalidHeaders.push({
          sheet: target,
          errors: headerErrors,
        });
        report.err = true;
      }
    }
  }

  return report;
};

/**
 * Import product configuration from an Excel file
 * @param {String} tenantId - Tenant ID
 * @param {File} file - Excel file containing product configuration
 * @param {String[]} formattedSheets - Array of main sheet names to import
 * @returns {Promise<Object>} { err: boolean, missingSheets: [], invalidHeaders: [], data: {} }
 */
exports.importProductConfiguration = async (
  tenantId,
  file,
  formattedSheets
) => {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(file.buffer);

  // Step 1: Validate workbook sheets and headers
  const validateReport = await validateWorkBook(workbook, formattedSheets);
  if (validateReport.err) {
    console.log("Validation Report:", JSON.stringify(validateReport, null, 2));
    return validateReport;
  }

  let aggregatedResult = {
    logEntry: [],
    missingSheets: [],
    invalidHeaders: [],
  };

  // Step 2: Expand sheets and form structured data
  for (const sheetName of formattedSheets) {
    const targetSheetNames = expandSheetNames(sheetName);
    const data = {};

    for (const target of targetSheetNames) {
      const worksheet = workbook.getWorksheet(target);
      if (!worksheet) {
        console.warn(`Missing worksheet: ${target}`);
        return {
          err: true,
          missingSheets: [target],
          invalidHeaders: [],
        };
      }

      const headers = getHeaders(target) || [];
      const rows = [];
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) return; // skip header row
        const item = {};
        headers.forEach((h, i) => {
          item[h.key] = row.values[i + 1] ?? null;
        });
        rows.push(item);
      });

      data[target] = rows;
    }

    const result = await saveData(sheetName, tenantId, data);

    result.logEntry.sheetName = sheetName;
    aggregatedResult.logEntry.push(result.logEntry);
  }

  return aggregatedResult;
};
