/**
 * Procurement Report Definitions
 * -------------------------------
 * Centralized constants and configuration for all procurement reports.
 * Each report defines its columns, sort behavior, and optional aggregate keys.
 */

const REPORTS = Object.freeze({
  // Procurement reports
  GRN: "grn-report",
  DETAILED_GRN: "grn-detailed-report",
  LOCATION_WISE_GRN: "grn-location-wise-report",
  VENDOR_WISE_GRN: "grn-vendor-wise-report",
  CATEGORY_WISE_GRN: "grn-category-wise-report",
  SUB_CATEGORY_WISE_GRN: "grn-sub-category-wise-report",
  ITEM_WISE_GRN: "grn-item-wise-report",
  DAILY_GRN: "grn-daily-report",

  // Transfers reports
  TRANSFER_LIST: "transfer-list-report",
  DISPATCH_TRANSFER: "dispatch-transfer-report",
  DETAILED_TRANSFER: "detailed-transfer-report",

  // stock movements
  LIVE_STOCK: "live-stock-report",
  STOCK_LEDGERS: "stock-ledgers",
  COST_OF_ISSUE_VS_REVENUE: "cost-of-issue-vs-revenue",
  ITEM_WISE_STOCK_MOVEMENTS: "item-wise-stock-movements",
  SHORT_SUPPLY: "short-supply",
});

const AGGREGATE_TYPES = {
  locationId: {
    id: "locationId",
    label: "locationName",
    columns: [],
  },
  inventoryLocationId: {
    id: "inventoryLocationId",
    label: "inventoryLocationName",
    columns: [],
  },
  vendorId: {
    id: "vendorName",
    label: "vendorName",
    columns: [],
  },
  date: {
    id: "date",
    label: "date",
    columns: ["grnDate"],
  },
  grnDate: {
    id: "grnDate",
    label: "grnDate",
    columns: [],
  },
  categoryId: {
    id: "categoryId",
    label: "categoryName",
    columns: [],
  },
  subCategoryId: {
    id: "subCategoryId",
    label: "subCategoryName",
    columns: ["categoryId", "categoryName"],
  },
  itemId: {
    id: ["itemId", "pkgId"],
    label: "itemName",
    columns: [
      "hsnCode",
      "itemCode",
      "pkg",
      "subCategoryId",
      "subCategoryName",
      "categoryId",
      "categoryName",
    ],
  },
};

/**
 * @typedef {Object} ReportColumn
 * @property {string} key        - Key or path in data object
 * @property {string} header     - Column display name
 * @property {number} ordinal    - Display order (1..N)
 * @property {boolean} mandatory - Cannot be removed
 * @property {boolean} enable    - Enabled by default
 * @property {number} [width]    - Excel column width
 * @property {"left"|"center"|"end"} [align] - Optional alignment
 * @property {string} [destruct] - Optional field name to flatten nested arrays
 */

const AMOUNT_DETAIL_COLUMNS = [
  {
    header: "Gross Amount",
    key: "grossAmount",
    ordinal: 91,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Discount",
    key: "totalDiscount",
    ordinal: 92,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Cess",
    key: "totalCess",
    ordinal: 93,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Net Amount",
    key: "netAmount",
    ordinal: 94,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Foc",
    key: "totalFocAmount",
    ordinal: 95,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },

  // { header: "Charge", key: "itemCharge", ordinal: 96, mandatory: false, enable: true, destruct: false, align: "end" },
  {
    header: "Taxes",
    key: "taxes",
    ordinal: 97,
    mandatory: false,
    enable: false,
    destruct: true,
    align: "end",
  },
  {
    header: "Total Tax",
    key: "totalTaxAmount",
    ordinal: 98,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Charges",
    key: "charges",
    ordinal: 99,
    mandatory: false,
    enable: false,
    destruct: true,
    align: "end",
  },
  {
    header: "Total Charges",
    key: "totalChargeAmount",
    ordinal: 100,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Total",
    key: "totalAmount",
    ordinal: 101,
    mandatory: true,
    enable: true,
    destruct: false,
    align: "end",
  },
];

/**
 * Example Column Sets
 */
const REPORT_INFORMATION = Object.freeze({
  [REPORTS.GRN]: {
    id: REPORTS.GRN,
    name: "GRN REPORT",
    headers: [
      {
        header: "GRN No",
        key: "grnNumber",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      // { header: "PO No", key: "poNumber", ordinal: 2, mandatory: true, enable: true, destruct: false },
      {
        header: "GRN DATE",
        key: "grnDate",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Invoice No",
        key: "invoiceNumber",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Invoice Date",
        key: "invoiceDate",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Vendor",
        key: "vendorName",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      // { header: "Vendor Id", key: "vendorId", ordinal: 6, mandatory: false, enable: true, destruct: false },
      {
        header: "Location",
        key: "locationName",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Work/Storage Area",
        key: "inventoryLocationName",
        ordinal: 8,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created Date",
        key: "createdDate",
        ordinal: 9,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created Time",
        key: "createdTime",
        ordinal: 10,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created By",
        key: "createdByName",
        ordinal: 11,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      ...AMOUNT_DETAIL_COLUMNS,
      // { header: "PO Terms", key: "poTerms", ordinal: 18, mandatory: false, enable: true, destruct: false },
      // { header: "Payment Terms", key: "paymentTerms", ordinal: 19, mandatory: false, enable: true, destruct: false },
    ],
    aggregateId: null,
    options: {
      sortBy: "grnNumber",
      sortOrder: "desc",
    },
  },

  [REPORTS.DETAILED_GRN]: {
    id: REPORTS.DETAILED_GRN,
    name: "DETAILED GRN REPORT",
    headers: [
      {
        header: "GRN No",
        key: "grnNumber",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      // { header: "PO No", key: "poNumber", ordinal: 2, mandatory: true, enable: true, destruct: false },
      {
        header: "GRN DATE",
        key: "grnDate",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Invoice No",
        key: "invoiceNumber",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Invoice Date",
        key: "invoiceDate",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Vendor",
        key: "vendorName",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      // {
      //   header: "Vendor Id",
      //   key: "vendorId",
      //   ordinal: 6,
      //   mandatory: false,
      //   enable: true,
      //   destruct: false,
      // },
      {
        header: "Location",
        key: "locationName",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Work/Storage Area",
        key: "inventoryLocationName",
        ordinal: 8,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created Date",
        key: "createdDate",
        ordinal: 9,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created Time",
        key: "createdTime",
        ordinal: 10,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created By",
        key: "createdByName",
        ordinal: 10,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "ItemName",
        key: "itemName",
        ordinal: 11,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "ItemCode",
        key: "itemCode",
        ordinal: 12,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "HSN Code",
        key: "hsnCode",
        ordinal: 12,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Category",
        key: "categoryName",
        ordinal: 13,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Sub Category",
        key: "subCategoryName",
        ordinal: 14,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      // { header: "Package", key: "pkg", ordinal: 14, mandatory: false, enable: true, destruct: false },
      // { header: "Order Quantity", key: "orderQuantity", ordinal: 15, mandatory: false, enable: true, destruct: false },
      {
        header: "Quantity",
        key: "qty",
        ordinal: 15,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      {
        header: "Package/UOM",
        key: "pkg",
        ordinal: 16,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Cost",
        key: "unitCost",
        ordinal: 17,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: null,
    options: {
      sortBy: "grnNumber",
      sortOrder: "desc",
    },
  },

  [REPORTS.VENDOR_WISE_GRN]: {
    id: REPORTS.VENDOR_WISE_GRN,
    name: "VENDOR-WISE GRN REPORT",
    headers: [
      {
        header: "Vendor",
        key: "vendorName",
        ordinal: 1,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "vendorId",
    options: {
      sortBy: "totalAmount",
      sortOrder: "desc",
    },
  },

  [REPORTS.CATEGORY_WISE_GRN]: {
    id: REPORTS.CATEGORY_WISE_GRN,
    name: "CATEGORY-WISE GRN REPORT",
    headers: [
      {
        header: "Category",
        key: "categoryName",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      // { header: "Sub Category", key: "subCategory", ordinal: 2, mandatory: true, enable: true, destruct: false },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "categoryId",
    options: {},
  },

  [REPORTS.SUB_CATEGORY_WISE_GRN]: {
    id: REPORTS.SUB_CATEGORY_WISE_GRN,
    name: "SUB-CATEGORY-WISE GRN REPORT",
    headers: [
      {
        header: "Sub Category",
        key: "subCategoryName",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Category",
        key: "categoryName",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },

      // { header: "Sub Category", key: "subCategory", ordinal: 2, mandatory: true, enable: true, destruct: false },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "subCategoryId",
    options: {},
  },

  [REPORTS.ITEM_WISE_GRN]: {
    id: REPORTS.ITEM_WISE_GRN,
    name: "ITEM-WISE GRN REPORT",
    headers: [
      {
        header: "Item Name",
        key: "itemName",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Item Code",
        key: "itemCode",
        ordinal: 2,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "HSN Code",
        key: "hsnCode",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Category",
        key: "categoryName",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Sub Category",
        key: "subCategoryName",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      // { header: "UOM", key: "uom", ordinal: 5, mandatory: true, enable: true, destruct: false },
      {
        header: "Quantity",
        key: "qty",
        ordinal: 7,
        mandatory: true,
        enable: true,
        destruct: false,
        align: "end",
      },
      {
        header: "Package/UOM",
        key: "pkg",
        ordinal: 8,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Unit Cost",
        key: "unitCost",
        ordinal: 9,
        mandatory: true,
        enable: true,
        destruct: false,
        align: "end",
      },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "itemId",
    options: {},
  },

  [REPORTS.LOCATION_WISE_GRN]: {
    id: REPORTS.LOCATION_WISE_GRN,
    name: "LOCATION-WISE GRN REPORT",
    headers: [
      {
        header: "Location Name",
        key: "locationName",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "locationId",
    options: {},
  },

  [REPORTS.DAILY_GRN]: {
    id: REPORTS.DAILY_GRN,
    name: "DAILY PURCHASE GRN REPORT",
    headers: [
      {
        header: "Date",
        key: "date",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "date",
    options: {},
  },

  [REPORTS.TRANSFER_LIST]: {
    id: REPORTS.TRANSFER_LIST,
    name: "TRANSFER REPORT",
    headers: [
      {
        header: "Transfer No",
        key: "transferNo",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "From Location",
        key: "fromLocation",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "From Work/StorageArea",
        key: "fromWorkArea",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "To Location",
        key: "toLocation",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: " To Work/StorageArea",
        key: "toWorkArea",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created Date",
        key: "createdDate",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created Time",
        key: "createdTime",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created By",
        key: "createdBy",
        ordinal: 6,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Dispatch Status",
        key: "dispatchStatus",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Receive Status",
        key: "receiveStatus",
        ordinal: 8,
        mandatory: false,
        enable: true,
        destruct: false,
      },
    ],
    aggregateKey: null,
    options: {
      sortBy: "transferNumber",
      sortOrder: "desc",
    },
  },

  [REPORTS.DISPATCH_TRANSFER]: {
    id: REPORTS.DISPATCH_TRANSFER,
    name: "DISPATCH TRANSFER REPORT",
    headers: [
      {
        header: "Transfer No",
        key: "transferNo",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Dispatch No",
        key: "dispatchNo",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Dispatched Date",
        key: "dispatchedDate",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Dispatched Time",
        key: "dispatchedTime",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Dispatched By",
        key: "dispatchedBy",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Receive Status",
        key: "status",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
    ],
    aggregateKey: null,
    options: {
      sortBy: "transferNumber",
      sortOrder: "desc",
    },
  },

  [REPORTS.DETAILED_TRANSFER]: {
    id: REPORTS.DETAILED_TRANSFER,
    name: "DETAILED TRANSFER REPORT",
    headers: [
      {
        header: "Transfer No",
        key: "transferNo",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "From Location",
        key: "fromLocation",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "From Work/StorageArea",
        key: "fromWorkArea",
        ordinal: 3,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "To Location",
        key: "toLocation",
        ordinal: 4,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "To Work/StorageArea",
        key: "toWorkArea",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created Date",
        key: "createdDate",
        ordinal: 6,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created Time",
        key: "createdTime",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created By",
        key: "createdBy",
        ordinal: 8,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Category",
        key: "categoryName",
        ordinal: 9,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Sub Category",
        key: "subcategoryName",
        ordinal: 10,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Item Name",
        key: "itemName",
        ordinal: 11,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Item Code",
        key: "itemCode",
        ordinal: 12,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Package",
        key: "pkg",
        ordinal: 13,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Requested Quantity",
        key: "requestedQuantity",
        ordinal: 14,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      {
        header: "Dispatched Quantity",
        key: "dispatchedQuantity",
        ordinal: 15,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      {
        header: "Received Quantity",
        key: "receivedQuantity",
        ordinal: 16,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      {
        header: "Shortage Quantity",
        key: "shortageQuantity",
        ordinal: 17,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      {
        header: "Shortage Reason",
        key: "shortageReason",
        ordinal: 18,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "center",
      },
      {
        header: "Rate",
        key: "unitCost",
        ordinal: 19,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "center",
      },
      {
        header: "Total Dispatched Cost",
        key: "totalDispatchCost",
        ordinal: 20,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      {
        header: "Total Received Cost",
        key: "totalReceiveCost",
        ordinal: 21,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
    ],
    aggregateKey: null,
    options: {
      sortBy: "transferNo", // Changed from "transferNumber" to match the key
      sortOrder: "desc",
    },
  },

  [REPORTS.COST_OF_ISSUE_VS_REVENUE]: {
    id: REPORTS.COST_OF_ISSUE_VS_REVENUE,
    name: "COST OF ISSUE VS REVENUE",
    headers: [
      {
        header: "Group",
        key: "group",
        ordinal: 1,
        mandatory: true,
        enable: true,
      },
      {
        header: "Sales",
        key: "sales",
        ordinal: 2,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Cost of Issue",
        key: "costOfIssue",
        ordinal: 3,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Costing %",
        key: "costingPercentage",
        ordinal: 4,
        mandatory: true,
        enable: true,
        align: "end",
      },
    ],
    options: {
      sortBy: "group",
      sortOrder: "asc",
    },
  },

  [REPORTS.ITEM_WISE_STOCK_MOVEMENTS]: {
    id: REPORTS.ITEM_WISE_STOCK_MOVEMENTS,
    name: "ITEM-WISE STOCK MOVEMENTS REPORT",
    headers: [
      {
        header: "Date",
        key: "businessDate",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Item Name",
        key: "itemName",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Item Code",
        key: "itemCode",
        ordinal: 3,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "HSN Code",
        key: "hsnCode",
        ordinal: 4,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Location",
        key: "location",
        ordinal: 5,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Work/StorageArea",
        key: "workArea",
        ordinal: 6,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Category",
        key: "categoryName",
        ordinal: 7,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Sub Category",
        key: "subCategoryName",
        ordinal: 8,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Package/UOM",
        key: "pkg",
        ordinal: 9,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      // {
      //   header: "Unit Cost",
      //   key: "unitCost",
      //   ordinal: 10,
      //   mandatory: true,
      //   enable: true,
      //   destruct: false,
      //   align: "end",
      // },

      // STOCK MOVEMENT FIELDS
      {
        header: "Opening Qty",
        key: "openingQty",
        ordinal: 11,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Purchase Qty",
        key: "purchaseQty",
        ordinal: 12,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Transfer In Qty",
        key: "transferInQty",
        ordinal: 13,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Transfer Out Qty",
        key: "transferOutQty",
        ordinal: 14,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Spoilage Qty",
        key: "spoilageQty",
        ordinal: 15,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Adjustment Qty",
        key: "adjustmentQty",
        ordinal: 16,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Consumption Qty",
        key: "consumptionQty",
        ordinal: 17,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Expected Qty",
        key: "expectedQty",
        ordinal: 18,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Closing Qty",
        key: "closingQty",
        ordinal: 19,
        mandatory: true,
        enable: true,
        align: "end",
      },

      {
        header: "Opening Amount",
        key: "openingAmount",
        ordinal: 20,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Purchase Amount",
        key: "purchaseAmount",
        ordinal: 21,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Transfer In Amount",
        key: "transferInAmount",
        ordinal: 22,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Transfer Out Amount",
        key: "transferOutAmount",
        ordinal: 23,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Spoilage Amount",
        key: "spoilageAmount",
        ordinal: 24,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Adjustment Amount",
        key: "adjustmentAmount",
        ordinal: 25,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Consumption Amount",
        key: "consumptionAmount",
        ordinal: 26,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Expected Amount",
        key: "expectedAmount",
        ordinal: 27,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Closing Amount",
        key: "closingAmount",
        ordinal: 28,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Variance Qty",
        key: "varianceQty",
        ordinal: 29,
        mandatory: true,
        enable: true,
        align: "end",
      },
      {
        header: "Variance Amount",
        key: "varianceAmount",
        ordinal: 30,
        mandatory: true,
        enable: true,
        align: "end",
      },
    ],

    aggregateId: "itemId",

    options: {
      sortBy: "itemName",
      sortOrder: "asc",
    },
  },

  [REPORTS.SHORT_SUPPLY]: {
    id: REPORTS.SHORT_SUPPLY,
    name: "SHORT SUPPLY REPORT",
    headers: [
      {
        header: "Transfer No",
        key: "transferNo",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Item Name",
        key: "itemName",
        ordinal: 2,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Item Code",
        key: "itemCode",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Package",
        key: "pkg",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Requested Quantity",
        key: "requestedQuantity",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "center",
      },
      {
        header: "Dispatched Quantity",
        key: "dispatchedQuantity",
        ordinal: 6,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "center",
      },
      {
        header: "Shortage Quantity",
        key: "shortageQuantity",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "center",
      },
    ],
    aggregateKey: null,
    options: {
      sortBy: "transferNumber",
      sortOrder: "desc",
    },
  },
  [REPORTS.LIVE_STOCK]: {
    id: REPORTS.LIVE_STOCK,
    name: "LIVE STOCK REPORT",
    headers: [
      {
        header: "Location",
        key: "locationName",
        align: "start",
        sortable: false,
      },
      {
        header: "Work/Storage Area",
        key: "inventoryLocationName",
        align: "start",
        sortable: false,
      },

      {
        header: "Category",
        key: "categoryName",
        align: "start",
        sortable: false,
      },
      {
        header: "Sub Category",
        key: "subCategoryName",
        align: "start",
        sortable: false,
      },
      { header: "Item Code", key: "itemCode", align: "start", sortable: false },
      {
        header: "Item Name",
        key: "itemName",
        align: "start",
        sortable: false,
      },
      {
        header: "Entry Type",
        key: "entryType",
        align: "start",
        sortable: false,
      },
      {
        header: "Package Name",
        key: "packageName",
        align: "start",
        sortable: false,
      },
      // {
      //   header: "UOM",
      //   key: "uom",
      //   align: "start",
      //   sortable: false,
      // },
      { header: "Store Stock", key: "qty", align: "end", sortable: false },
      {
        header: "Rate(Incl. Tax)",
        key: "avgCost",
        align: "end",
        sortable: false,
      },
      {
        header: "Store Total(Incl. Tax)",
        key: "totalCost",
        align: "end",
        sortable: false,
      },
      {
        header: "workArea",
        key: "workArea",
        align: "end",
        enable: false,
        destruct: true,
        subKeys: ["qty", "cost"],
      },
    ],
  },
  [REPORTS.STOCK_LEDGERS]: {
    id: REPORTS.STOCK_LEDGERS,
    name: "STOCK LEDGERS",
    headers: [
      { header: "Location", key: "locationName", align: "start" },
      {
        header: "Work/Storage Area",
        key: "inventoryLocationName",
        align: "start",
      },
      {
        header: "Created At",
        key: "createdAt",
        align: "start",
        sortable: false,
      },
      {
        header: "Item Name",
        key: "itemName",
        align: "start",
        sortable: false,
      },
      { header: "Type", key: "type", align: "start", sortable: false },
      { header: "Quantity", key: "qty", align: "end", sortable: false },
      {
        header: "Package",
        key: "uom",
        align: "start",
        sortable: false,
      },
      {
        header: "Total",
        key: "totalCost",
        align: "start",
        sortable: false,
      },
      { header: "Summary", key: "remarks", align: "center", sortable: false },
    ],
  },
});

module.exports = {
  REPORTS,
  REPORT_INFORMATION,
  AGGREGATE_TYPES,
};
