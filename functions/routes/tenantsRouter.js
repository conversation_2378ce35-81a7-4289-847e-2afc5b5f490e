const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const { getHome, searchByNumber } = require("@/controllers/homeController.js");
const {
  getMasterData,
  getMasterInventoryItemData,
} = require("@/controllers/masterController.js");

const {
  importProductConfiguration,
  exportProductConfiguration,
  getImportExportLogs,
} = require("@/controllers/productImportExport.js");

const {
  updateTenantSettings,
  freezeMonth,
  getTenant,
} = require("@/controllers/tenantController.js");

// product configuration import/export
router.get(
  "/export-product-configuration",
  checkPrivilege(PRIV_CODES.PC_EXPORT),
  exportProductConfiguration
);
router.post(
  "/import-product-configuration",
  checkPrivilege(PRIV_CODES.PC_IMPORT),
  importProductConfiguration
);
router.get(
  "/import-export-logs",
  checkPrivilege(PRIV_CODES.PC_IMPORT),
  getImportExportLogs
);

router.get("/master", getMasterData);
router.get("/master/inventory-items", getMasterInventoryItemData);
router.get("/action-center/summary", getHome);
router.get("/action-center/search", searchByNumber); // @Todo: change to searchByNumber

router.post("/setting", updateTenantSettings);
router.post("/freeze", freezeMonth);
router.get("/", getTenant);

// tenant-scoped subroutes (array-driven)
[
  ["accounts", "./accounts"],
  ["stores", "./stores"],
  ["vendors", "./vendors"],
  ["inventory-locations", "./inventory-locations"],
  ["house-units", "./house-units"],
  ["taxes", "./taxes"],
  ["charges", "./charges"],
  ["categories", "./categories"],
  ["inventory-items", "./inventory-items"],
  ["recipes", "./recipes"],
  ["menu-items", "./menu-items"],
  ["purchase-requests", "./purchase-requests"],
  ["purchase-orders", "./purchase-orders"],
  ["modifiers", "./modifiers"],
  ["roles", "./roles"],
  ["users", "./users"],
  ["reports", "./reports"],
  ["transfers", "./transfers"],
  ["purchases", "./purchases"],
  ["tags", "./tags"],
  ["ledgers", "./productLedgers"],
  ["closing", "./closing"],
  ["adjustments", "./adjustments"],
  ["spoilages", "./spoilages"],
  ["contracts", "./contracts"],
  ["tenants", "./tenants"],
  ["attachments", "./attachments"],
  ["dashboards", "./dashboards"],
  ["menuRecipes", "./menuRecipes"],
  ["report-groups", "./report-groups"],
  ["tally", "./tally"],
].forEach(([path, module]) => {
  router.use(`/${path}`, require(module));
});

module.exports = router;
