const express = require("express");
const cors = require("cors");
const { fileParser } = require("express-multipart-file-parser");

// Controllers & Middlewares
const { silentLogin } = require("@/controllers/authController.js");
const inviteController = require("@/controllers/inviteController");
const {
  validateToken,
  validateTenant,
} = require("@/middlewares/authMiddleware.js");
const deleteController = require("@/controllers/deleteController");

const app = express();

app.use(
  cors({
    origin: true,
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: [
      "Authorization",
      "Content-Type",
      "x-tenant-id",
      "app-id", // for tally
    ],
  })
);

app.use(fileParser({ rawBodyOptions: { limit: "10mb" } }));

// Auth
app.post("/silent-login", silentLogin);

// Invite callback
app.post("/invite/callback/:id", inviteController.inviteCallback);

// 🔹 TALLY ROUTES (ADD THIS)
app.use("/api/tally", require("./tally"));

// Tenant master
app.use("/tenants", require("./tenants.js"));

// Tenant scoped (JWT protected)
app.use(
  "/tenants/:tenantId",
  validateToken,
  validateTenant,
  require("./tenantsRouter.js")
);

// delete by filter (only for dev testing)
app.post("/delete", deleteController.deleteByFilter);

app.post("/addWorkAreaName", deleteController.addInventoryLocationNameInStore);
app.post("/updateStock", deleteController.updateStockPkgId);
app.post("/updateStockLedgers", deleteController.updateStockLedgers);
app.post("/updateGRN", deleteController.updateGRN);
app.post("/updatePO", deleteController.updatePO);
app.post("/updatePR", deleteController.updatePR);
app.post("/updateTransfer", deleteController.updateTransfer);
app.post("/updateClosing", deleteController.updateClosing);
app.post("/updateStockId", deleteController.updateStockId);

module.exports = app;
