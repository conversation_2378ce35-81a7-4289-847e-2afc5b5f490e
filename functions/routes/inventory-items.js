const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const inventoryItemController = require("@/controllers/inventoryItemController");

router.get(
  "/:id",
  checkPrivilege(PRIV_CODES.PC_VIEW),
  inventoryItemController.getInventoryItemById
);
router.get(
  "/",
  checkPrivilege(PRIV_CODES.PC_VIEW),
  inventoryItemController.getInventoryItems
);
router.post(
  "/:id/details",
  inventoryItemController.getInventoryItemDetails
);
router.post(
  "/",
  checkPrivilege(PRIV_CODES.PC_EDIT),
  inventoryItemController.insertInventoryItem
);

router.put(
  "/:id/activate",
  checkPrivilege(PRIV_CODES.PC_EDIT),
  inventoryItemController.activateItem
);
router.put(
  "/:id/deactivate",
  checkPrivilege(PRIV_CODES.PC_EDIT),
  inventoryItemController.deactivateItem
);

router.put(
  "/:id",
  checkPrivilege(PRIV_CODES.PC_EDIT),
  inventoryItemController.updateInventoryItem
);

// Get Transfer detail by ID
router.get("/transferDetails/:id", inventoryItemController.getTransferDetails);

module.exports = router;
