/**
 * Stock Movement Reports Routes
 * --------------------------
 * Routes for tenant-level stock movement reports such as GRN, vendor, and purchase summaries.
 */

const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const stockMovementReportController = require("@/controllers/stockMovementReportsController");

// All routes assume :tenantId is provided in the parent route
// Example: /:tenantId/reports/stockMovements/...

// Transfer List Report
router.post(
  "/transfer-list-report",
  checkPrivilege(PRIV_CODES.REP_TRA),
  stockMovementReportController.getTransferListReport
);

// Dispatch Transfer Report
router.post(
  "/dispatch-transfer-report",
  checkPrivilege(PRIV_CODES.REP_TRA),
  stockMovementReportController.getDispatchTransferReport
);

// Detailed Transfer Report
router.post(
  "/detailed-transfer-report",
  checkPrivilege(PRIV_CODES.REP_TRA),
  stockMovementReportController.getDetailedTransferReport
);

// Cost of Issue vs Revenue
router.post(
  "/cost-of-issue-vs-revenue",
  checkPrivilege(PRIV_CODES.REP_TRA),
  stockMovementReportController.getCostOfIssueVsRevenueReport
);

// Item Wise Stock Movement
router.post(
  "/item-wise-stock-movements",
  checkPrivilege(PRIV_CODES.REP_TRA),
  stockMovementReportController.getItemWiseStockMovementsReport
);

// Short supply Report
// requested vs issued
router.post(
  "/short-supply-report",
  checkPrivilege(PRIV_CODES.REP_TRA),
  stockMovementReportController.getShortSupplyReport
);

// Physical Closing Report
router.post(
  "/physical-closing-report",
  checkPrivilege(PRIV_CODES.REP_TRA),
  stockMovementReportController.getPhysicalClosingReport
);

// System Closing Report
router.post(
  "/system-closing-report",
  checkPrivilege(PRIV_CODES.REP_TRA),
  stockMovementReportController.getSystemClosingReport
);

module.exports = router;
