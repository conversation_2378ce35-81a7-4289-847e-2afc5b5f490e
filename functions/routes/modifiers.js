const express = require("express");
const router = express.Router({ mergeParams: true });

const modifierController = require("@/controllers/modifierController");

router.get("/", modifierController.getModifiers);
router.post("/", modifierController.insertModifiers);
router.get("/:id", modifierController.getModifierById);
router.put("/:id", modifierController.updateModifier);

module.exports = router;
