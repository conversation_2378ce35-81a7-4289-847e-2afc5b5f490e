const express = require("express");
const router = express.Router({ mergeParams: true });

const {
  getStocks,
  getStockLedgers
} = require("@/controllers/stockReportController");
const { checkPrivilege } = require("@/middlewares/authMiddleware");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

router.post("/stocks", checkPrivilege(PRIV_CODES.STK_VIEW), getStocks);

router.post(
  "/stock-ledgers",
  checkPrivilege(PRIV_CODES.STK_VIEW),
  getStockLedgers
);

//  procurement reports
router.use("/procurements", require("./procurement-reports"));

//  stock movement reports
router.use("/stock-movements", require("./stockMovement-reports"));

module.exports = router;
