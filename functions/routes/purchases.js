// routes/purchaseRoutes.js

const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const purchaseController = require("@/controllers/purchaseController");
const grnController = require("@/controllers/grnController");

// -------------------
// GRN Routes
// -------------------
// Create GRN (Receive items against a PO)
router.post("/create-grn", purchaseController.receivePO);

// Get GRN detail by ID
router.get("/grns/:id", grnController.getGRNByID);

// Get GRN detail by Number
router.get("/grns/number/:grnNumber", grnController.getGRNByNumber);

// Print GRN detail by ID
// router.get('/grns/:id/print', grnController.printGRNByID);

// Get GRNs (report/listing with filters)
router.post("/grns/last-purchase-prices", grnController.getGrnItemPrices);
router.post("/grns/last-price", grnController.getLastGrnItemPrice);
router.post("/grns/:id/delete", grnController.deleteGRNByID);
router.post("/grns/:id/return-vendor", grnController.returnToVendor);
router.post("/grns", checkPrivilege(PRIV_CODES.PUR_GRN), grnController.getGRNs);

router.put("/grn/:id/attachments", grnController.updateGrnAttachments);
router.delete("/grn/:id/attachments", grnController.deleteGrnAttachment);
router.post("/grns/:id", grnController.updateGRN);

router.get(
  "/:id/pdf",
  checkPrivilege(PRIV_CODES.PUR_GRN),
  grnController.exportToPDF
);

router.get(
  "/:id/xlsx",
  checkPrivilege(PRIV_CODES.PUR_GRN),
  grnController.exportToXLSX
);

module.exports = router;
