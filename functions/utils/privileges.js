// privileges.js
const { PRIV_CODES } = require("@/defs/privilegeDefs");

// Step 1: Grouped privileges by module
const groupedPrivileges = {
  Dashboard: [
    {
      code: PRIV_CODES.DASH_PUR,
      label: "Purchase Dashboard",
      description: "Overview of purchase activities."
    },
    {
      code: PRIV_CODES.DASH_COGS,
      label: "COGS Dashboard",
      description: "Overview of COGS and cost analysis."
    },
    {
      code: PRIV_CODES.DASH_INV,
      label: "Inventory Dashboard",
      description: "Overview of inventory activities."
    }
  ],
  "Product Configuration": [
    {
      code: PRIV_CODES.PC_VIEW,
      label: "View Product",
      description:
        "Allows the user to view all product configurations without making changes."
    },
    {
      code: PRIV_CODES.PC_EDIT,
      label: "Edit Product",
      description:
        "Allows the user to create new or edit existing product configurations."
    },
    {
      code: PRIV_CODES.PC_IMPORT,
      label: "Import Products",
      description:
        "Allows the user to import products from external files or sources."
    },
    {
      code: PRIV_CODES.PC_EXPORT,
      label: "Export Products",
      description:
        "Allows the user to export product data for reporting or backup."
    }
  ],

  Procurement: [
    {
      code: PRIV_CODES.PUR_PR,
      label: "Purchase Requests",
      description:
        "Allows the user to create, edit, and view purchase requests."
    },
    {
      code: PRIV_CODES.PUR_APPROVE_PR,
      label: "PR APPROVAL",
      description: "Approve purchase requests."
    },
    {
      code: PRIV_CODES.PUR_PO,
      label: "Purchase Orders",
      description:
        "Allows the user to create, approve, and manage purchase orders."
    },
    {
      code: PRIV_CODES.PUR_APPROVE_PO,
      label: "PO APPROVAL",
      description: "Approve purchase orders."
    },
    {
      code: PRIV_CODES.PUR_GRN,
      label: "GRN Management",
      description:
        "Allows the user to manage Goods Receipt Notes and record stock receipts."
    }
  ],

  "Stock Movement": [
    {
      code: PRIV_CODES.TRANSFER,
      label: "Transfer Management",
      description: "Handle transfer activities."
    },
    {
      code: PRIV_CODES.TRA_EDIT,
      label: "Edit Transfer",
      description:
        "Some storekeepers will not be permitted to create transfers, they will have view and dispatch access only"
    },
    {
      code: PRIV_CODES.TRA_DISPATCH,
      label: "Dispatch Transfer",
      description: "Only Store/Purchase team will dispatch"
    },
    {
      code: PRIV_CODES.TRA_RECEIVE,
      label: "Receive Transfer",
      description:
        "Transfers will be received only by the receiving end (Chefs/Managers...)"
    },
    {
      code: PRIV_CODES.PREPARATION,
      label: "Preparation Management",
      description: "Handle preparation activities."
    },

    {
      code: PRIV_CODES.PRE_EDIT,
      label: "Add Preparation",
      description: "Allow users to add preparation"
    },
    {
      code: PRIV_CODES.SPOILAGE,
      label: "Spoilage Management",
      description: "Handle spoilage activities."
    },
    {
      code: PRIV_CODES.SPO_EDIT,
      label: "Add Spoilage",
      description: "Allow users to add spoilage"
    },
    {
      code: PRIV_CODES.ADJUSTMENT,
      label: "Adjustment Management",
      description: "Handle adjustment activities."
    },
    {
      code: PRIV_CODES.ADJ_EDIT,
      label: "Add Adjustment",
      description: "Allow users to add adjustment"
    },
    {
      code: PRIV_CODES.CLOSING,
      label: "Closing Management",
      description: "Handle closing activities."
    },
    {
      code: PRIV_CODES.CLO_EDIT,
      label: "Add Closing",
      description: "Allow users to add closing"
    }
  ],

  Stock: [
    {
      code: PRIV_CODES.STK_VIEW,
      label: "Stock Overview",
      description:
        "Allows the user to view current stock levels and item details."
    }
  ],

  Report: [
    {
      code: PRIV_CODES.REP_GRN,
      label: "GRN Reports",
      description:
        "Allows the user to generate and view reports related to GRNs."
    },
    {
      code: PRIV_CODES.REP_TRA,
      label: "Transfer Reports",
      description:
        "Allows the user to generate and view reports related to transfer."
    }
    // {
    //   code: PRIV_CODES.REP_STK,
    //   label: "Stock Reports",
    //   description: "Generate/view stock reports."
    // }
  ],

  Setup: [
    {
      code: PRIV_CODES.SET_LOC,
      label: "Locations",
      description:
        "Allows the user to manage inventory or operational locations."
    },
    {
      code: PRIV_CODES.SET_USER,
      label: "User Management",
      description:
        "Allows the user to manage system users, roles, and access permissions."
    },
    {
      code: PRIV_CODES.CONTRACT,
      label: "Contracts",
      description: "Allows the user to manage about contracts."
    },
    {
      code: PRIV_CODES.REP_GROUP,
      label: "Report Groups",
      description: "Generate/view report groups."
    }
  ],
  Settings: [
    {
      code: PRIV_CODES.SETTING,
      label: "Settings",
      description: "Allow users to manage settings."
    }
  ],
  Recipe: [
    {
      code: PRIV_CODES.RECIPE_VIEW,
      label: "View Recipe",
      description: " Allows the user to view all recipe"
    },
    {
      code: PRIV_CODES.RECIPE_EDIT,
      label: "Edit Recipe",
      description: " Allows the user to create new or edit existing recipe"
    }
  ]
};

function getAllPrivileges() {
  return Object.values(groupedPrivileges).flat();
}

function validateUserPrivilege(isAdmin, userPrivileges, requiredCodes) {
  if (isAdmin) return true;
  if (!Array.isArray(requiredCodes)) requiredCodes = [requiredCodes];
  return requiredCodes.every((code) => userPrivileges.includes(code));
}

function getPrivilegeDetails(codes) {
  const allPrivileges = getAllPrivileges();
  return codes
    .map((code) => allPrivileges.find((p) => p.code === code))
    .filter(Boolean);
}

module.exports = {
  groupedPrivileges,
  validateUserPrivilege,
  getPrivilegeDetails
};
