const Joi = require("joi");

const timestampSchema = Joi.alternatives().try(
  Joi.date().iso(),
  Joi.number().integer(),
  Joi.object({
    _seconds: Joi.number().integer().required(),
    _nanoseconds: Joi.number().integer().min(0).max(999999999).required(),
  })
);

const userSchema = Joi.object({
  userName: Joi.string().required(),
  userId: Joi.string().required(),
  time: timestampSchema.optional()
});

const itemSchema = Joi.object({
  itemId: Joi.string().required(),
  itemName: Joi.string().max(100).required(),
  itemCode: Joi.string().max(50).required(),
  categoryId: Joi.string().required(),
  subcategoryId: Joi.string().required(),
  categoryName: Joi.string().max(100).required(),
  subcategoryName: Joi.string().max(100).required(),
  unitCost: Joi.number().optional(),
  purchaseUOM: Joi.string().optional(),
  countingUOM: Joi.string().required(),
  recipeUOM: Joi.string().optional(),
  conversionFactor: Joi.number().optional(),
  openQuantities: Joi.array().required(),
  closingQuantity: Joi.number()
    .min(0)
    .required()
    .messages({ "number.min": "Closing Quantity Should be Greater than Zero" }),
    pkg: Joi.object({
      id: Joi.string().required(),
      name: Joi.string().required(),
      packageCode: Joi.string().optional(),
      quantity: Joi.number().optional(),
      unitCost: Joi.number().optional(),
      emptyWeight: Joi.number().optional(),
      fullWeight: Joi.number().optional(),
      toUnit: Joi.string().optional()
    }).required(),
    
});

const closingSchema = Joi.object({
  id: Joi.string(),
  tenantId: Joi.string().required(),
  locationId: Joi.string().required(),
  locationName: Joi.string().required(),
  workAreaId: Joi.string().required(),
  workAreaName: Joi.string().required(),
  closingNumber: Joi.string().optional(),
  closingDate: Joi.date().iso().required(),
  items: Joi.array()
    .items(itemSchema)
    .min(1)
    .required()
    .messages({ "array.min": "At Least One Item is Required" }),
    stockCorrection: Joi.boolean().required(),
  closedBy: userSchema.required(),
});

module.exports = closingSchema;
