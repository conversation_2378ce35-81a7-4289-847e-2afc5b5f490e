const admin = require("firebase-admin");
const schema = require("@/models/modifierSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.MODIFIERS);
const { handleValidation } = require("@/utils/validation");

exports.getModifiers = async (req, res) => {
  const tenantId = req.params?.tenantId?.trim();
  if (!tenantId) {
    return res.status(400).json({ message: "Field tenantId is required." });
  }

  try {
    const data = await db.where("tenantId", "==", tenantId).get();
    const modifiers = data.docs.map((doc) => doc.data());
    return res.status(200).json(modifiers);
  } catch (error) {
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.getModifierById = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.params?.tenantId?.trim();
  const accountId = req.query?.accountId?.trim();

  if (!id || !tenantId || !accountId) {
    return res
      .status(400)
      .json({ message: "id, tenantId, and accountId are required." });
  }

  try {
    const snapshot = await db
      .where("tenantId", "==", tenantId)
      .where("account.id", "==", accountId)
      .where("id", "==", id)
      .get();

    if (snapshot.empty) {
      return res.status(404).json({ message: "Modifier not found." });
    }
    const result = [];
    snapshot.forEach((doc) => {
      result.push({ id: doc.id, ...doc.data() });
    });
    return res.status(200).json(result[0]);
  } catch (error) {
    console.error("Error fetching modifier by ID:", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.updateModifier = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.body?.tenantId?.trim();
  const accountId = req.query?.accountId?.trim();
  const updatedData = req.body;

  if (!id || !tenantId || !accountId) {
    return res
      .status(400)
      .json({ message: "id, tenantId, and accountId are required." });
  }

  try {
    const validatedData = handleValidation(updatedData, schema);
    if (!validatedData) return;

    const querySnapshot = await db
      .where("tenantId", "==", tenantId)
      .where("account.id", "==", accountId)
      .where("id", "==", id)
      .limit(1)
      .get();

    if (querySnapshot.empty) {
      return res.status(404).json({ message: "Modifier not found." });
    }

    const doc = querySnapshot.docs[0];
    const tenant = doc.data();

    if (tenant?.tenantId !== tenantId) {
      return res
        .status(403)
        .json({ message: "Unauthorized access to update this modifier." });
    }

    await doc.ref.update({ ...validatedData, linkingStatus: true });
    return res.status(200).json({ message: "Modifier updated successfully." });
  } catch (error) {
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.insertModifiers = async (req, res) => {
  try {
    const input = req.body;
    const modifiers = Array.isArray(input) ? input : [input];

    const accountDb = admin.firestore().collection(COLLECTIONS.ACCOUNT);

    const accSnapshot = await accountDb
      .where("posId", "==", modifiers[0].accountId)
      .get();
    const account = accSnapshot.docs[0].data();

    if (!account) {
      return res.status(404).json({ message: "Account not found." });
    }

    const preparedModifiers = modifiers.map((modifier) => ({
      posId: modifier.posId,
      itemName: modifier.itemName,
      servingLevels: modifier.servingLevels ?? [],
      account: {
        id: account.id,
        name: account.name,
        posId: account.posId,
      },
      tenantId: account.tenantId,
    }));

    const validModifiers = await this.insertModifierLogic(
      preparedModifiers,
      res
    );

    res.status(201).json({
      message: "Modifiers inserted/updated successfully",
      count: validModifiers.length,
    });
  } catch (error) {
    console.error("Error inserting modifiers:", error);
    res.status(500).send(error.message || "Internal Server Error");
  }
};

exports.insertModifierLogic = async (modifiers, res) => {
  const batch = admin.firestore().batch();
  const validModifiers = [];

  for (const mod of modifiers) {
    const validatedData = handleValidation(mod, schema);
    if (!validatedData) {
      console.log("insertModifierLogic validation failed:", mod);
      return;
    }

    const modifierSnap = await db
      .where("posId", "==", validatedData.posId)
      .where("account.id", "==", validatedData.account.id)
      .limit(1)
      .get();

    if (!modifierSnap.empty) {
      const existingDoc = modifierSnap.docs[0];
      const docRef = db.doc(existingDoc.id);
      batch.update(docRef, validatedData);
    } else {
      const docRef = db.doc(); // Auto ID
      batch.set(docRef, { ...validatedData, id: docRef.id });
    }

    validModifiers.push(validatedData);
  }

  await batch.commit();
  return validModifiers;
};
