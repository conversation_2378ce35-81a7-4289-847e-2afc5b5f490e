const admin = require("firebase-admin");
const db = admin.firestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

exports.deleteByFilter = async (req, res) => {
  try {
    const { collectionName, field, value } = req.body;

    if (
      !Array.isArray(collectionName) ||
      collectionName.length === 0 ||
      !field ||
      value === undefined
    ) {
      return res.status(400).json({
        error:
          "Missing or invalid parameters: collectionName (array), field, value",
      });
    }

    let totalDeleted = 0;

    // Loop through each collection in the array
    for (const name of collectionName) {
      let deletedInCollection = 0;
      let batchCount = 0;

      while (true) {
        const snapshot = await db
          .collection(name)
          .where(field, "==", value)
          .limit(500)
          .get();

        if (snapshot.empty) break;

        const batch = db.batch();
        snapshot.docs.forEach((doc) => batch.delete(doc.ref));

        await batch.commit();

        deletedInCollection += snapshot.size;
        totalDeleted += snapshot.size;
        batchCount++;

        console.log(
          `Collection '${name}' → Batch ${batchCount}: deleted ${snapshot.size} docs`
        );
      }

      console.log(
        `Collection '${name}' completed. Total deleted: ${deletedInCollection}`
      );
    }

    return res.json({
      message: `Deleted ${totalDeleted} documents from ${collectionName.length} collection(s) where ${field} = ${value}`,
    });
  } catch (error) {
    console.error("Error deleting documents:", error);
    return res.status(500).json({ error: error.message });
  }
};

exports.addInventoryLocationNameInStore = async (req, res) => {
  try {
    const storesSnap = await db.collection("stores").get();

    if (storesSnap.empty) {
      return res.status(200).json({
        success: true,
        message: "No stores found",
        updatedCount: 0,
        skippedCount: 0,
      });
    }

    const stores = storesSnap.docs.map((d) => ({
      id: d.id,
      ref: d.ref,
      ...d.data(),
    }));

    // 1️⃣ Collect unique inventoryLocationIds
    const locationIds = [
      ...new Set(stores.map((s) => s.inventoryLocationId).filter(Boolean)),
    ];

    // 2️⃣ Fetch all locations in parallel
    const locationSnaps = await Promise.all(
      locationIds.map((id) => db.collection("inventoryLocations").doc(id).get())
    );

    // 3️⃣ Build lookup map
    const locationMap = new Map();
    for (let i = 0; i < locationSnaps.length; i++) {
      const snap = locationSnaps[i];
      if (snap.exists) {
        locationMap.set(locationIds[i], snap.data());
      }
    }

    const batch = db.batch();
    let updatedCount = 0;
    let skippedCount = 0;

    // 4️⃣ Update stores using map
    for (const store of stores) {
      const loc = locationMap.get(store.inventoryLocationId);

      if (!loc) {
        skippedCount++;
        continue;
      }

      batch.update(store.ref, {
        inventoryLocationName: loc.name || null,
      });

      updatedCount++;
    }

    if (updatedCount === 0) {
      return res.status(200).json({
        success: true,
        message: "No stores required update",
        updatedCount,
        skippedCount,
      });
    }

    await batch.commit();

    return res.status(200).json({
      success: true,
      message: "Inventory location names added to stores",
      updatedCount,
      skippedCount,
    });
  } catch (error) {
    console.error("addInventoryLocationNameInStore failed:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to add inventory location names",
      error: error.message || String(error),
    });
  }
};

const getInventoryData = async (tenantId) => {
  const snapshot = await db
    .collection(COLLECTIONS.INVENTORY_ITEMS)
    .where("tenantId", "==", tenantId)
    .get();

  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};

const getStockData = async (tenantId) => {
  const snapshot = await db
    .collection(COLLECTIONS.STOCKS)
    .where("tenantId", "==", tenantId)
    .get();

  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};

const getStockLedgers = async (tenantId) => {
  const snapshot = await db
    .collection(COLLECTIONS.LEDGERS)
    .where("tenantId", "==", tenantId)
    .get();

  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};

const getGRNs = async (tenantId) => {
  const snapshot = await db
    .collection(COLLECTIONS.GRN)
    .where("tenantId", "==", tenantId)
    .get();

  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};

const getPO = async (tenantId) => {
  const snapshot = await db
    .collection(COLLECTIONS.PURCHASE_ORDERS)
    .where("tenantId", "==", tenantId)
    .get();

  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};

const getPR = async (tenantId) => {
  const snapshot = await db
    .collection(COLLECTIONS.PURCHASE_REQUESTS)
    .where("tenantId", "==", tenantId)
    .get();

  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};

const getTransfers = async (tenantId) => {
  const snapshot = await db
    .collection(COLLECTIONS.TRANSFERS)
    .where("tenantId", "==", tenantId)
    .get();

  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};

const getClosing = async (tenantId) => {
  const snapshot = await db
    .collection(COLLECTIONS.CLOSING)
    .where("tenantId", "==", tenantId)
    .get();

  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};

exports.updateStockPkgId = async (req, res) => {
  try {
    const collectionName = COLLECTIONS.STOCKS;

    const { tenantId } = req.body;
    if (!tenantId) return res.status(400).json({ error: "tenantId required" });

    const [inventoryItems, stockData] = await Promise.all([
      getInventoryData(tenantId),
      getStockData(tenantId),
    ]);

    // --------------------------------------------
    // Build lookup map: itemId -> packageName -> pkgId
    // --------------------------------------------
    const itemPkgMap = new Map();

    for (const item of inventoryItems) {
      if (!item.id || !Array.isArray(item.packages)) continue;

      const pkgMap = new Map();
      for (const pkg of item.packages) {
        if (pkg?.name && pkg?.id) {
          pkgMap.set(pkg.name, pkg.id);
        }
      }
      itemPkgMap.set(item.id, pkgMap);
    }

    let batch = db.batch();
    let batchCount = 0;
    let updated = 0;

    for (const stock of stockData) {
      if (!stock.itemId || !stock.pkg?.name) continue;

      const pkgMap = itemPkgMap.get(stock.itemId);
      if (!pkgMap) continue;

      const pkgId = pkgMap.get(stock.pkg.name);
      if (!pkgId) continue;

      if (stock.pkg.id === pkgId) continue;

      const ref = db.collection(collectionName).doc(stock.id);
      batch.update(ref, { "pkg.id": pkgId, updatedAt: new Date() });

      batchCount++;
      updated++;

      if (batchCount >= 450) {
        await batch.commit();
        batch = db.batch(); // <-- new batch
        batchCount = 0; // <-- reset counter
      }
    }

    if (batchCount > 0) {
      await batch.commit();
    }

    return res.status(200).json({
      message: "Parsing completed",
      updated,
      inventoryCount: inventoryItems.length,
      stockCount: stockData.length,
    });
  } catch (error) {
    console.error("Error parsing data:", error);
    return res.status(500).json({ error: error.message });
  }
};

exports.updateStockLedgers = async (req, res) => {
  try {
    const collectionName = COLLECTIONS.LEDGERS;
    const { tenantId } = req.body;
    if (!tenantId) return res.status(400).json({ error: "tenantId required" });

    const [inventoryItems, stockData] = await Promise.all([
      getInventoryData(tenantId),
      getStockLedgers(tenantId),
    ]);

    // --------------------------------------------
    // Build lookup: itemId -> pkgName -> pkgId
    // --------------------------------------------
    const itemPkgMap = new Map();

    for (const item of inventoryItems) {
      if (!item.id || !Array.isArray(item.packages)) continue;

      const pkgMap = new Map();
      for (const pkg of item.packages) {
        if (pkg?.name && pkg?.id) {
          pkgMap.set(pkg.name, pkg.id);
        }
      }
      itemPkgMap.set(item.id, pkgMap);
    }

    let batch = db.batch();
    let batchCount = 0;
    let updated = 0;

    for (const stock of stockData) {
      if (!stock.itemId || !stock.pkg?.name) continue;

      const pkgMap = itemPkgMap.get(stock.itemId);
      if (!pkgMap) continue;

      const pkgId = pkgMap.get(stock.pkg.name);
      if (!pkgId) continue;

      if (stock.pkg.id === pkgId) continue;

      const ref = db.collection(collectionName).doc(stock.id);
      batch.update(ref, { "pkg.id": pkgId, updatedAt: new Date() });

      batchCount++;
      updated++;

      if (batchCount >= 450) {
        await batch.commit();
        batch = db.batch(); // <-- new batch
        batchCount = 0; // <-- reset counter
      }
    }

    if (batchCount > 0) {
      await batch.commit();
    }

    return res.status(200).json({
      message: "Parsing completed",
      updated,
      inventoryCount: inventoryItems.length,
      stockLedgerCount: stockData.length,
    });
  } catch (error) {
    console.error("Error parsing stock ledgers:", error);
    return res.status(500).json({ error: error.message });
  }
};

exports.updateGRN = async (req, res) => {
  try {
    const collectionName = COLLECTIONS.GRN;
    const { tenantId } = req.body;
    if (!tenantId) return res.status(400).json({ error: "tenantId required" });

    const [inventoryItems, grnData] = await Promise.all([
      getInventoryData(tenantId),
      getGRNs(tenantId),
    ]);

    // --------------------------------------------
    // Build lookup: itemId -> pkgName -> pkgId
    // --------------------------------------------
    const itemPkgMap = new Map();

    for (const item of inventoryItems) {
      if (!item.id || !Array.isArray(item.packages)) continue;

      const pkgMap = new Map();
      for (const pkg of item.packages) {
        if (pkg?.name && pkg?.id) {
          pkgMap.set(pkg.name, pkg.id);
        }
      }
      itemPkgMap.set(item.id, pkgMap);
    }

    let batch = db.batch();
    let batchCount = 0;
    let updated = 0;

    for (const grn of grnData) {
      let mutated = false;

      if (!Array.isArray(grn.items)) continue;

      for (const item of grn.items) {
        if (!item?.itemId || !item?.pkg?.name) continue;

        const pkgMap = itemPkgMap.get(item.itemId);
        if (!pkgMap) continue;

        const correctPkgId = pkgMap.get(item.pkg.name);
        if (!correctPkgId) continue;

        if (item.pkg.id !== correctPkgId) {
          item.pkg.id = correctPkgId;
          mutated = true;
        }
      }

      if (!mutated) continue;

      const ref = db.collection(collectionName).doc(grn.id);
      batch.update(ref, { items: grn.items });
      batchCount++;
      updated++;

      if (batchCount >= 450) {
        await batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    }

    if (batchCount > 0) {
      await batch.commit();
    }

    return res.status(200).json({
      message: "Parsing completed",
      updated,
      inventoryCount: inventoryItems.length,
      GRNCount: grnData.length,
    });
  } catch (error) {
    console.error("Error updating GRN:", error);
    return res.status(500).json({ error: error.message });
  }
};

exports.updatePO = async (req, res) => {
  try {
    const collectionName = COLLECTIONS.PURCHASE_ORDERS;
    const { tenantId } = req.body;
    if (!tenantId) return res.status(400).json({ error: "tenantId required" });

    const [inventoryItems, poData] = await Promise.all([
      getInventoryData(tenantId),
      getPO(tenantId),
    ]);

    // --------------------------------------------
    // Build lookup: itemId -> pkgName -> pkgId
    // --------------------------------------------
    const itemPkgMap = new Map();

    for (const item of inventoryItems) {
      if (!item.id || !Array.isArray(item.packages)) continue;

      const pkgMap = new Map();
      for (const pkg of item.packages) {
        if (pkg?.name && pkg?.id) {
          pkgMap.set(pkg.name, pkg.id);
        }
      }
      itemPkgMap.set(item.id, pkgMap);
    }

    let batch = db.batch();
    let batchCount = 0;
    let updated = 0;

    for (const grn of poData) {
      let mutated = false;

      if (!Array.isArray(grn.items)) continue;

      for (const item of grn.items) {
        if (!item?.itemId || !item?.pkg?.name) continue;

        const pkgMap = itemPkgMap.get(item.itemId);
        if (!pkgMap) continue;

        const correctPkgId = pkgMap.get(item.pkg.name);
        if (!correctPkgId) continue;

        if (item.pkg.id !== correctPkgId) {
          item.pkg.id = correctPkgId;
          mutated = true;
        }
      }

      if (!mutated) continue;

      const ref = db.collection(collectionName).doc(grn.id);
      batch.update(ref, { items: grn.items });
      batchCount++;
      updated++;

      if (batchCount >= 450) {
        await batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    }

    if (batchCount > 0) {
      await batch.commit();
    }

    return res.status(200).json({
      message: "Parsing completed",
      updated,
      inventoryCount: inventoryItems.length,
      POCount: poData.length,
    });
  } catch (error) {
    console.error("Error updating GRN:", error);
    return res.status(500).json({ error: error.message });
  }
};

exports.updatePR = async (req, res) => {
  try {
    const collectionName = COLLECTIONS.PURCHASE_REQUESTS;
    const { tenantId } = req.body;
    if (!tenantId) return res.status(400).json({ error: "tenantId required" });

    const [inventoryItems, prData] = await Promise.all([
      getInventoryData(tenantId),
      getPR(tenantId),
    ]);

    // --------------------------------------------
    // Build lookup: itemId -> pkgName -> pkgId
    // --------------------------------------------
    const itemPkgMap = new Map();

    for (const item of inventoryItems) {
      if (!item.id || !Array.isArray(item.packages)) continue;

      const pkgMap = new Map();
      for (const pkg of item.packages) {
        if (pkg?.name && pkg?.id) {
          pkgMap.set(pkg.name, pkg.id);
        }
      }
      itemPkgMap.set(item.id, pkgMap);
    }

    let batch = db.batch();
    let batchCount = 0;
    let updated = 0;

    for (const grn of prData) {
      let mutated = false;

      if (!Array.isArray(grn.items)) continue;

      for (const item of grn.items) {
        if (!item?.itemId || !item?.pkg?.name) continue;

        const pkgMap = itemPkgMap.get(item.itemId);
        if (!pkgMap) continue;

        const correctPkgId = pkgMap.get(item.pkg.name);
        if (!correctPkgId) continue;

        if (item.pkg.id !== correctPkgId) {
          item.pkg.id = correctPkgId;
          mutated = true;
        }
      }

      if (!mutated) continue;

      const ref = db.collection(collectionName).doc(grn.id);
      batch.update(ref, { items: grn.items });
      batchCount++;
      updated++;

      if (batchCount >= 450) {
        await batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    }

    if (batchCount > 0) {
      await batch.commit();
    }

    return res.status(200).json({
      message: "Parsing completed",
      updated,
      inventoryCount: inventoryItems.length,
      PRCount: prData.length,
    });
  } catch (error) {
    console.error("Error updating GRN:", error);
    return res.status(500).json({ error: error.message });
  }
};

exports.updateTransfer = async (req, res) => {
  try {
    const collectionName = COLLECTIONS.TRANSFERS;
    const { tenantId } = req.body;
    if (!tenantId) return res.status(400).json({ error: "tenantId required" });

    const [inventoryItems, transferData] = await Promise.all([
      getInventoryData(tenantId),
      getTransfers(tenantId),
    ]);

    // --------------------------------------------
    // Build lookup: itemId -> pkgName -> pkgId
    // --------------------------------------------
    const itemPkgMap = new Map();

    for (const item of inventoryItems) {
      if (!item.id || !Array.isArray(item.packages)) continue;

      const pkgMap = new Map();
      for (const pkg of item.packages) {
        if (pkg?.name && pkg?.id) {
          pkgMap.set(pkg.name, pkg.id);
        }
      }
      itemPkgMap.set(item.id, pkgMap);
    }

    let batch = db.batch();
    let batchCount = 0;
    let updated = 0;

    for (const transfer of transferData) {
      let mutated = false;

      // ---- Top-level transfer items ----
      if (Array.isArray(transfer.items)) {
        for (const item of transfer.items) {
          if (!item?.itemId || !item?.pkg?.name) continue;

          const pkgMap = itemPkgMap.get(item.itemId);
          if (!pkgMap) continue;

          const correctPkgId = pkgMap.get(item.pkg.name);
          if (!correctPkgId) continue;

          if (item.pkg.id !== correctPkgId) {
            item.pkg.id = correctPkgId;
            mutated = true;
          }
        }
      }

      // ---- Timeline nested items ----
      if (Array.isArray(transfer.timeLine)) {
        for (const entry of transfer.timeLine) {
          if (!Array.isArray(entry.items)) continue;

          for (const item of entry.items) {
            if (!item?.itemId || !item?.pkg?.name) continue;

            const pkgMap = itemPkgMap.get(item.itemId);
            if (!pkgMap) continue;

            const correctPkgId = pkgMap.get(item.pkg.name);
            if (!correctPkgId) continue;

            if (item.pkg.id !== correctPkgId) {
              item.pkg.id = correctPkgId;
              mutated = true;
            }
          }
        }
      }

      if (!mutated) continue;

      const ref = db.collection(collectionName).doc(transfer.id);
      batch.update(ref, {
        items: transfer.items || [],
        timeLine: transfer.timeLine || [],
      });

      batchCount++;
      updated++;

      if (batchCount >= 450) {
        await batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    }

    if (batchCount > 0) {
      await batch.commit();
    }

    return res.status(200).json({
      message: "Transfer parsing completed",
      updated,
      inventoryCount: inventoryItems.length,
      transferCount: transferData.length,
    });
  } catch (error) {
    console.error("Error updating Transfer:", error);
    return res.status(500).json({ error: error.message });
  }
};

exports.updateClosing = async (req, res) => {
  try {
    const collectionName = COLLECTIONS.CLOSING;
    const { tenantId } = req.body;
    if (!tenantId) return res.status(400).json({ error: "tenantId required" });

    const [inventoryItems, closingData] = await Promise.all([
      getInventoryData(tenantId),
      getClosing(tenantId),
    ]);

    // --------------------------------------------
    // Build lookup: itemId -> pkgName -> pkgId
    // --------------------------------------------
    const itemPkgMap = new Map();

    for (const item of inventoryItems) {
      if (!item.id || !Array.isArray(item.packages)) continue;

      const pkgMap = new Map();
      for (const pkg of item.packages) {
        if (pkg?.name && pkg?.id) {
          pkgMap.set(pkg.name, pkg.id);
        }
      }
      itemPkgMap.set(item.id, pkgMap);
    }

    let batch = db.batch();
    let batchCount = 0;
    let updated = 0;

    for (const closing of closingData) {
      let mutated = false;

      if (!Array.isArray(closing.items)) continue;

      for (const item of closing.items) {
        if (!item?.itemId || !item?.pkg?.name) continue;

        const pkgMap = itemPkgMap.get(item.itemId);
        if (!pkgMap) continue;

        const correctPkgId = pkgMap.get(item.pkg.name);
        if (!correctPkgId) continue;

        if (item.pkg.id !== correctPkgId) {
          item.pkg.id = correctPkgId;
          mutated = true;
        }
      }

      if (!mutated) continue;

      const ref = db.collection(collectionName).doc(closing.id);
      batch.update(ref, { items: closing.items });
      batchCount++;
      updated++;

      if (batchCount >= 450) {
        await batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    }

    if (batchCount > 0) {
      await batch.commit();
    }

    return res.status(200).json({
      message: "Parsing completed",
      updated,
      inventoryCount: inventoryItems.length,
      closingCount: closingData.length,
    });
  } catch (error) {
    console.error("Error updating GRN:", error);
    return res.status(500).json({ error: error.message });
  }
};

exports.updateStockId = async (req, res) => {
  try {
    const collectionName = COLLECTIONS.STOCKS;
    const { tenantId } = req.body;
    if (!tenantId) return res.status(400).json({ error: "tenantId required" });

    const stockData = await getStockData(tenantId);

    let batch = db.batch();
    let batchCount = 0;
    let migrated = 0;
    let skipped = 0;

    for (const stock of stockData) {
      if (!stock.inventoryLocationId || !stock.itemId || !stock.pkg?.id) {
        skipped++;
        continue;
      }

      const requiredId = `${stock.inventoryLocationId}_${stock.itemId}_${stock.pkg.id}`;

      if (stock.id === requiredId) {
        skipped++;
        continue;
      }

      const oldRef = db.collection(collectionName).doc(stock.id);
      const newRef = db.collection(collectionName).doc(requiredId);

      // Create new document with same data + new id
      const newData = {
        ...stock,
        id: requiredId,
        requiredId,
        migratedAt: new Date(),
      };

      delete newData._firestoreId; // if any accidental metadata
      delete newData.__name__;

      batch.set(newRef, newData, { merge: false });
      batch.delete(oldRef);

      batchCount += 2; // set + delete
      migrated++;

      if (batchCount >= 450) {
        await batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    }

    if (batchCount > 0) {
      await batch.commit();
    }

    return res.status(200).json({
      message: "Stock ID migration completed",
      migrated,
      skipped,
      total: stockData.length,
    });
  } catch (error) {
    console.error("Error migrating stock IDs:", error);
    return res.status(500).json({ error: error.message });
  }
};
