// controllers/stockReportController.js

const { aggregateStocks, getLedgers } = require("@/services/stockService");
const { constructColumns } = require("@/helpers/reportHelper");
const { REPORTS, REPORT_INFORMATION } = require("@/defs/reportDefs");
const { render, ResultType } = require("@/helpers/render");
const { createXlsxReport } = require("@/helpers/xlsxReportUtility");
const { validateIdentityFilters } = require("@/helpers/reportHelper");

/**
 * Get aggregated stocks
 * @param {Object} req.body - Must contain tenantId, and optionally locationId, inventoryLocationId, categoryId, subCategoryId, itemId
 * @returns {Promise<Object>} - Promise that resolves to a JSON object containing the aggregated stocks
 */
exports.getStocks = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const { resultType = ResultType.JSON } = req.query;

    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenantId is required",
      });
    }
    const reportInfo = REPORT_INFORMATION[REPORTS.LIVE_STOCK];
    if (!reportInfo) throw new Error("Invalid report type");

    const _filters = validateIdentityFilters(req.identity, req.body.filters);

    const { results, meta } = await aggregateStocks(tenantId, _filters);

    const headers = constructColumns(
      reportInfo.headers,
      req.body.columns,
      meta,
      null,
      resultType
    );

    const output = {
      data: results,
      payload: req.body,
      headers: headers,
      expand: false,
      id: reportInfo.id,
      name: reportInfo.name,
      tenantId: tenantId,
      totalRow: null,
    };

    switch (resultType) {
      case ResultType.EXCEL:
        const resBuffer = await createXlsxReport(output);
        render(res, resultType, resBuffer, reportInfo.id);
        return;
      default:
        render(res, resultType, output, reportInfo.id);
    }
  } catch (error) {
    console.error("Error fetching stocks:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.getStockLedgers = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const { resultType = ResultType.JSON } = req.query;

    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenantId is required",
      });
    }

    const reportInfo = REPORT_INFORMATION[REPORTS.STOCK_LEDGERS];
    if (!reportInfo) throw new Error("Invalid report type");

    const _filters = validateIdentityFilters(req.identity, req.body.filters);

    const result = await getLedgers(tenantId, _filters);

    const headers = constructColumns(
      reportInfo.headers,
      req.body.columns,
      {},
      null,
      resultType
    );

    const output = {
      data: result,
      payload: req.body,
      headers: headers,
      expand: false,
      id: reportInfo.id,
      name: reportInfo.name,
      tenantId: tenantId,
      totalRow: null,
    };

    switch (resultType) {
      case ResultType.EXCEL:
        const resBuffer = await createXlsxReport(output);
        render(res, resultType, resBuffer, reportInfo.id);
        return;
      default:
        render(res, resultType, output, reportInfo.id);
    }
  } catch (error) {
    console.error("Error fetching stocks:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};


exports.getConsumptionDetails = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const { resultType = ResultType.JSON } = req.query;

    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenantId is required",
      });
    }

    const reportInfo = REPORT_INFORMATION[REPORTS.CONSUMPTION_TRACKING];
    if (!reportInfo) throw new Error("Invalid report type");

    const _filters = validateIdentityFilters(req.identity, req.body.filters);

    // const result = await getLedgers(tenantId, _filters);


    const result = [
      {
        "saleReferenceId": 35020,
        "accountId": 326,
        "storeId": 477,
        "ticketNo": "1",
        "tableNo": "14401",
        "floorNo": 535,
        "posMenuItemName": "Coke",
        "posItemCode": "DIGI-5104",
        "servingSizeId": 2839,
        "servingSizeName": "200ml",
        "soldQuantity": 2,
        "posItemTotalAmount": 2000,
        "workAreaId": "r3F1glutpumL0u04W2s7",
        "workArea": "Kitchen",
        "locationId": "UaA7SHz6pMQmDLtYwsMp",
        "location": "Chennai",
        "status": "pending",
        "statusSummary": "Ready for consumption",
        "invItems": [
          {
            "itemName": "coke",
            "itemCode": "I0012",
            "itemType": "bought",
            "recipeUom": "ml",
            "requiredQty": 400,
            "cost": 0
          }
        ]
      },
      {
        "saleReferenceId": 35021,
        "accountId": 326,
        "storeId": 477,
        "ticketNo": "2",
        "tableNo": "14401",
        "floorNo": 535,
        "posMenuItemName": "Coke",
        "posItemCode": "DIGI-5104",
        "servingSizeId": 2839,
        "servingSizeName": "200ml",
        "soldQuantity": 6,
        "posItemTotalAmount": 6000,
        "workAreaId": "r3F1glutpumL0u04W2s7",
        "workArea": "Kitchen",
        "locationId": "UaA7SHz6pMQmDLtYwsMp",
        "location": "Chennai",
        "status": "pending",
        "statusSummary": "Ready for consumption",
        "invItems": [
          {
            "itemName": "coke",
            "itemCode": "I0012",
            "itemType": "bought",
            "recipeUom": "ml",
            "requiredQty": 1200,
            "cost": 0
          }
        ]
      },
      {
        "saleReferenceId": 35021,
        "accountId": 326,
        "storeId": 477,
        "ticketNo": "2",
        "tableNo": "14401",
        "floorNo": 535,
        "posMenuItemName": "pulav",
        "posItemCode": "DIGI-5105",
        "servingSizeId": 2838,
        "servingSizeName": "Each",
        "soldQuantity": 3,
        "posItemTotalAmount": 27000,
        "workAreaId": null,
        "workArea": null,
        "locationId": null,
        "location": null,
        "status": "error",
        "statusSummary": "workArea mapping required",
        "invItems": [
          {
            "itemName": "pulav",
            "itemCode": "I0013",
            "itemType": "made",
            "recipeUom": "g",
            "requiredQty": 1200,
            "cost": 0
          }
        ]
      }
    ];


    const headers = constructColumns(
      reportInfo.headers,
      req.body.columns,
      {},
      null,
      resultType
    );

    const output = {
      data: result,
      payload: req.body,
      headers: headers,
      expand: false,
      id: reportInfo.id,
      name: reportInfo.name,
      tenantId: tenantId,
      totalRow: null,
    };

    switch (resultType) {
      case ResultType.EXCEL:
        const resBuffer = await createXlsxReport(output);
        render(res, resultType, resBuffer, reportInfo.id);
        return;
      default:
        render(res, resultType, output, reportInfo.id);
    }
  } catch (error) {
    console.error("Error fetching stocks:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};
