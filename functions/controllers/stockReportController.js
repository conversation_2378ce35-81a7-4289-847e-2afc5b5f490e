// controllers/stockReportController.js

const { aggregateStocks, getLedgers } = require("@/services/stockService");
const { constructColumns } = require("@/helpers/reportHelper");
const { REPORTS, REPORT_INFORMATION } = require("@/defs/reportDefs");
const { render, ResultType } = require("@/helpers/render");
const { createXlsxReport } = require("@/helpers/xlsxReportUtility");

/**
 * Get aggregated stocks
 * @param {Object} req.body - Must contain tenantId, and optionally locationId, inventoryLocationId, categoryId, subCategoryId, itemId
 * @returns {Promise<Object>} - Promise that resolves to a JSON object containing the aggregated stocks
 */
exports.getStocks = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const { resultType = ResultType.JSON } = req.query;

    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenantId is required",
      });
    }
    const reportInfo = REPORT_INFORMATION[REPORTS.LIVE_STOCK];
    if (!reportInfo) throw new Error("Invalid report type");

    const { results, meta } = await aggregateStocks(tenantId, req.body.filters);

    const headers = constructColumns(
      reportInfo.headers,
      req.body.columns,
      meta,
      null,
      resultType
    );

    const output = {
      data: results,
      payload: req.body,
      headers: headers,
      expand: false,
      id: reportInfo.id,
      name: reportInfo.name,
      tenantId: tenantId,
      totalRow: null,
    };

    switch (resultType) {
      case ResultType.EXCEL:
        const resBuffer = await createXlsxReport(output);
        render(res, resultType, resBuffer, reportInfo.id);
        return;
      default:
        render(res, resultType, output, reportInfo.id);
    }
  } catch (error) {
    console.error("Error fetching stocks:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.getStockLedgers = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const { resultType = ResultType.JSON } = req.query;

    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenantId is required",
      });
    }

    const reportInfo = REPORT_INFORMATION[REPORTS.STOCK_LEDGERS];
    if (!reportInfo) throw new Error("Invalid report type");

    const result = await getLedgers(tenantId, req.body.filters);

    const headers = constructColumns(
      reportInfo.headers,
      req.body.columns,
      {},
      null,
      resultType
    );

    const output = {
      data: result,
      payload: req.body,
      headers: headers,
      expand: false,
      id: reportInfo.id,
      name: reportInfo.name,
      tenantId: tenantId,
      totalRow: null,
    };

    switch (resultType) {
      case ResultType.EXCEL:
        const resBuffer = await createXlsxReport(output);
        render(res, resultType, resBuffer, reportInfo.id);
        return;
      default:
        render(res, resultType, output, reportInfo.id);
    }
  } catch (error) {
    console.error("Error fetching stocks:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};
