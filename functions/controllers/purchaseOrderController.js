const { purchaseStatus } = require("@/defs/purchaseStatusDefs");
const { renderPDF } = require("@/helpers/render");
const {
  generatePrint,
  generateHeader,
  generateDetails,
  generateShipToAndSignature,
  generateItemsTable,
  generateGrandTotalWords,
  generateCombinedTermsSection,
  generateRemarks,
  generateDynamicTable,
} = require("@/helpers/printHelper");

const {
  aggregatePurchaseOrders,
  createPR,
  createPO,
  approvePO,
  rejectPO,
  getPurchaseOrderById,
  updatePurchaseOrder,
  closePO,
} = require("@/services/purchaseService");
const { preparePOData, getLocationById } = require("@/services/printService");

const { validateEmail } = require("@/utils/validation");
const {
  buildEmailTemplate,
  buildMailOptions,
  sendMail,
} = require("@/helpers/emailHelper");

const { validateIdentityFilters } = require("@/helpers/reportHelper");


exports.createPurchaseOrder = async (req, res) => {
  try {
    const { status: action, vendor, ...rest } = req.body;
    const payload = {
      ...rest,
      tenantId: req.params.tenantId,
      requestedBy: {
        id: req.identity.userId,
        name: req.identity.userName,
      },
    };

    payload.items = payload.items.map((item) => ({
      ...item,
      vendor: { id: vendor.id, name: vendor.name },
    }));

    let purchaseRequest = await createPR({
      ...payload,
      status: purchaseStatus.COMPLETED,
    });

    const poDocs = await createPO({
      ...purchaseRequest,
      action,
      prNumber: purchaseRequest.prNumber,
      prStatus: purchaseRequest.status,
      paymentTerms: vendor.paymentTerms,
      poTerms: vendor.poTerms,
      remarks: payload.remarks,
    });

    res.status(201).json({
      message: "Purchase Orders created",
      purchaseOrders: poDocs,
    });
  } catch (error) {
    console.error("Error creating purchase orders:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.approvePurchaseOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const { userName, userId } = req.identity;

    const approvedBy = {
      id: userId,
      name: userName,
    };

    if (!approvedBy.id || !approvedBy.name) {
      throw Error("Missing approvedBy");
    }

    const result = await approvePO(id, approvedBy);

    if (!result) {
      throw Error("Purchase order not found");
    }

    res.status(200).json({ message: "Purchase order approved" });
  } catch (error) {
    console.error("Error in approvePurchaseOrder:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.rejectPurchaseOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const { rejectedReason } = req.body;
    const { userName, userId } = req.identity;
    const rejectedBy = {
      id: userId,
      name: userName,
    };

    if (!rejectedBy || !rejectedBy.id || !rejectedBy.name) {
      return res
        .status(400)
        .json({ message: "Missing rejectedBy (name & id)" });
    }

    const result = await rejectPO(id, rejectedBy, rejectedReason);

    if (!result) {
      throw Error("Purchase order not found");
    }

    res.status(200).json({ message: "Purchase order rejected" });
  } catch (error) {
    console.error("Error in rejectPurchaseOrder:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.closePurchaseOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const { userName, userId } = req.identity;
    const closedBy = {
      id: userId,
      name: userName,
    };

    if (!closedBy || !closedBy.id || !closedBy.name) {
      return res.status(400).json({ message: "Missing closedBy (name & id)" });
    }

    const result = await closePO(id, closedBy, reason);

    if (!result) {
      throw Error("Purchase Order not found");
    }

    res.status(200).json({ message: "Purchase Order Closed" });
  } catch (error) {
    console.error("Error in closePurchaseOrder:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getPurchaseOrders = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    if (!tenantId) {
      return res.status(400).json({
        message: "tenantId is required",
      });
    }

    const filters = {
      status: req.body.status || null,
      locations: req.body.locations || null,
      inventoryLocations: req.body.inventoryLocations || null,
      fromDate: req.body.fromDate || null,
      toDate: req.body.toDate || null,
      vendors: req.body.vendors || null,
    };
    const _filters = validateIdentityFilters(req.identity, filters);

    const result = await aggregatePurchaseOrders(tenantId, _filters);
    res.status(200).json(result);
  } catch (error) {
    console.error("Error:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getPurchaseOrderById = async (req, res) => {
  try {
    const { id } = req.params;
    const purchaseOrder = await getPurchaseOrderById(id);

    if (!purchaseOrder) {
      return res.status(404).json({ message: "purchase order not found" });
    }

    res.status(200).json(purchaseOrder);
  } catch (error) {
    console.error("Error fetching purchase order:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updatePurchaseOrder = async (req, res) => {
  const payload = {
    ...req.body,
    updatedBy: {
      id: req.identity.userId,
      name: req.identity.userName,
    },
  };
  try {
    // @todo have to complete
    await updatePurchaseOrder(req.params.id, payload);
    res.status(200).json({ message: "updated successfully" });
  } catch (err) {
    console.error("Error updating purchase request:", err);
    res.status(500).json({ message: err.message });
  }
};

/**
 * Generates and returns purchase order PDF content
 */
async function generatePdfContent(id, tenantId) {
  const prepared = await preparePOData(id, tenantId);
  if (!prepared) throw new Error("No Purchase Order found.");

  const {
    po,
    vendor,
    vendorObj,
    poObj,
    poTerms,
    paymentTerms,
    remarks,
    grandTotal,
    itemsTable,
  } = prepared;
  const locationData = await getLocationById(tenantId, po.location.id);

  const content = [
    generateHeader("Purchase Order", po.tenantName, locationData?.name),
    generateDetails(vendorObj, poObj),
    generateShipToAndSignature(vendor),
    // generateItemsTable(po.items),
    generateDynamicTable(itemsTable),
    // generateGrandTotalWords(grandTotal),
    generateCombinedTermsSection(poTerms, paymentTerms),
    generateRemarks(remarks),
  ];

  return { prepared, content, locationData, grandTotal };
}

/**
 * Send an email
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {String} req.params.tenantId - Tenant ID
 * @param {String} req.params.id - Purchase Order ID
 * @returns {Promise<void>}
 */
exports.sendEmail = async (req, res) => {
  try {
    const { tenantId, id } = req.params;

    const { prepared, content, locationData } = await generatePdfContent(
      id,
      tenantId
    );

    const pdf = await generatePrint(content);
    const type = "Purchase Order";
    const templateName = "purchaseTemplate.html";
    const subject = `Purchase Order - ${prepared.po?.tenantName || ""} - ${
      locationData?.name || ""
    }`;
    const recipients = validateEmail(prepared.vendor?.contactEmailId);
    const htmlTemplate = buildEmailTemplate(prepared, templateName);

    const mailOptions = buildMailOptions({
      pdf,
      subject,
      htmlTemplate,
      type,
      recipients,
    });

    await sendMail(mailOptions);

    res.status(200).json({
      message: "Email sent successfully to vendor",
    });
  } catch (error) {
    console.error("Error sending email:", error);
    res.status(500).json({ message: error.message });
  }
};

/**
 * Exports a purchase order to a PDF file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {String} req.params.tenantId - Tenant ID
 * @param {String} req.params.id - Purchase Order ID
 * @returns {Promise<void>}
 */
exports.exportToPDF = async (req, res) => {
  try {
    const { tenantId, id } = req.params;

    const { prepared, content } = await generatePdfContent(id, tenantId);

    const pdf = await generatePrint(content);
    renderPDF(res, pdf, `${prepared.po.poNumber}.pdf`);
  } catch (error) {
    console.error("Error in exportToPDF:", error);
    res.status(500).json({ message: error.message });
  }
};

/**
 * Exports a purchase order to a XLSX file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {String} req.params.tenantId - Tenant ID
 * @param {String} req.params.id - Purchase Order ID
 * @returns {Promise<void>}
 */
exports.exportToXLSX = async (req, res) => {
  try {
    // @todo implement
    throw new Error("Not implemented");
  } catch (error) {
    console.error("Error in exportPdf:", error);
    res.status(500).json({ message: error.message });
  }
};
