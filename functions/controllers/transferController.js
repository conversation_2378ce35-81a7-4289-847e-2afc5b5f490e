const {
  createTransferRequest,
  createDispatch,
  getTransfer,
  receiveTransfer,
  aggregateTransfers,
  closeTransfer
} = require("@/services/transferService");

const { getTransferDataById } = require("@/repositories/transferRepo");

const { renderPDF } = require("@/helpers/render");

const { validateIdentityFilters } = require("@/helpers/reportHelper");

const {
  generatePrint,
  generateHeader,
  generateDetails,
  generateShipToAndSignature,
  generateTransferTable
} = require("@/helpers/printHelper");

const {
  prepareTransferData,
  getLocationById
} = require("@/services/printService");

const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

exports.createTransfer = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    if (!tenantId) {
      return res
        .status(400)
        .json({ success: false, message: "tenantId is required" });
    }

    const { userName, userId } = req.identity;
    const data = {
      ...req.body,
      tenantId,
      requestedBy: {
        id: userId,
        name: userName,
        time: FD.now()
      }
    };

    const indentRequest = await createTransferRequest(data);

    res.status(201).json(indentRequest);
  } catch (error) {
    console.error("error creating indent request", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getTransfers = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;

    if (!tenantId) {
      return res
        .status(400)
        .json({ success: false, message: "tenantId is required" });
    }

    const filters = {
      status: req.body.status || null,
      requester: req.body.requester || null,
      issuer: req.body.issuer || null,
      fromDate: req.body.fromDate || null,
      toDate: req.body.toDate || null,
      transferType: req.body.transferType || null
    };

    const _filters = validateIdentityFilters(req.identity, filters);

    const result = await aggregateTransfers(tenantId, _filters);

    return res.status(200).json(result);
  } catch (error) {
    console.error("Error fetching transfers:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.getTransferById = async (req, res) => {
  try {
    const data = await getTransfer(req.params.id, req.query.type);
    res.status(200).json(data);
  } catch (error) {
    console.log("error getting indent by id", error);
    res.status(500).json({ message: error.message });
  }
};

exports.dispatchTransfer = async (req, res) => {
  try {
    const { userName, userId } = req.identity;

    const transferIns = await getTransferDataById(req.params.id);
    if (!transferIns) {
      return res.status(404).json({ message: "Transfer not found" });
    }
    await createDispatch(req.body, transferIns, userId, userName);

    res.status(200).json("updated successfully");
  } catch (error) {
    console.error("dispatchTransfer error:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.receiveTransfer = async (req, res) => {
  try {
    const { userName, userId } = req.identity;
    const { dispatchId } = req.query;

    const transferIns = await getTransferDataById(req.params.id);

    await receiveTransfer(req.body, transferIns, dispatchId, userId, userName);

    res.status(200).json("updated successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.closeTransfer = async (req, res) => {
  try {
    const { userName, userId } = req.identity;

    const data = {
      closingDetails: {
        id: userId,
        name: userName,
        time: FD.now()
      }
    };
    await closeTransfer(data, req.params.id);
    res.status(200).json("updated successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

/**
 * Exports a transfer to a PDF file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {String} req.params.tenantId - Tenant ID
 * @param {String} req.params.id - Transfer ID
 * @returns {Promise<void>}
 */
exports.exportToPDF = async (req, res) => {
  try {
    const { tenantId, id } = req.params;
    const { dispatchNo } = req.query;

    let dispatchEntryItems = [];

    const prepared = await prepareTransferData(id, "Transfer", dispatchNo);
    if (!prepared) throw new Error("No Transfer Record found.");

    const { transfer, transferObj, type } = prepared;

    if (dispatchNo) {
      const dispatchEntry = transfer.timeLine?.find(
        (entry) => entry.dispatchNo === dispatchNo
      );
      dispatchEntryItems = dispatchEntry?.items || [];
    }

    const locationData = await getLocationById(tenantId, transfer.requester.id);

    const content = [
      generateHeader(type, transfer.tenantName, locationData?.name),
      generateDetails(transferObj),
      generateShipToAndSignature(locationData),
      generateTransferTable(type, transfer.items, dispatchEntryItems)
    ];

    const pdf = await generatePrint(content);
    renderPDF(res, pdf, `${transfer.transferNumber}.pdf`);
  } catch (error) {
    console.error("Error in exportPdf:", error);
    res.status(500).json({ message: error.message });
  }
};

/**
 * Exports a transfer to a XLSX file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {String} req.params.tenantId - Tenant ID
 * @param {String} req.params.id - Transfer ID
 * @returns {Promise<void>}
 */
exports.exportToXLSX = async (req, res) => {
  try {
    // @todo implement
    throw new Error("Not implemented");
  } catch (error) {
    console.error("Error in exportPdf:", error);
    res.status(500).json({ message: error.message });
  }
};
