const Joi = require("joi");
const { contractTypes } = require("@/defs/contractTypesDefs");
const attachmentSchema = require("@/models/attachmentSchema");

const symbolPattern = /^[^\s]{1,10}$/;

const timestampSchema = Joi.alternatives().try(
  Joi.date().iso(),
  Joi.number().integer(),
  Joi.object({
    _seconds: Joi.number().integer().required(),
    _nanoseconds: Joi.number().integer().min(0).max(999999999).required(),
  })
);

const userSchema = Joi.object({
  name: Joi.string().required(),
  id: Joi.string().required(),
});

// const closingSchema = Joi.object({
//   name: Joi.string().optional(),
//   id: Joi.string().optional(),
//   time: timestampSchema.optional(),
// });

const itemSchema = Joi.object({
  itemId: Joi.string().required(),
  itemName: Joi.string().required(),
  itemCode: Joi.string().required(),
  contractType: Joi.string()
    .valid(contractTypes.FIXED, contractTypes.DISCOUNT, contractTypes.ABSOLUTE)
    .required(),
  contractPrice: Joi.number().precision(2).required(),
  countingUOM: Joi.string()
    .max(10)
    .pattern(symbolPattern)
    .required()
    .messages({ "string.pattern.base": "Invalid Symbol" }),
  inclTax: Joi.boolean().required(),
  pkg: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
    packageCode: Joi.string().optional(),
    unitCost: Joi.number().optional(),
    quantity: Joi.number().positive().optional(),
    toUnit: Joi.string().optional(),
    emptyWeight: Joi.number().default(0).optional(),
    fullWeight: Joi.number().default(0).optional(),
  })
    .allow(null)
    .optional(),
});

const contractSchema = Joi.object({
  tenantId: Joi.string().required(),
  id: Joi.string().optional(),
  name: Joi.string().required(),
  nameNormalized: Joi.string().optional(),
  contractNumber: Joi.string().optional(),
  reference: Joi.string().required(),
  vendor: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
  }),
  location: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        name: Joi.string().required(),
      })
    )
    .min(1)
    .required(),
  startDate: timestampSchema.required(),
  endDate: timestampSchema.required(),
  requestedBy: userSchema.required(),
  items: Joi.array().items(itemSchema).min(1).required(),
  activeStatus: Joi.boolean().default(true),
  createdAt: timestampSchema.optional(),
  updatedAt: timestampSchema.optional(),
  attachments: Joi.array().items(attachmentSchema).optional(),
});

module.exports = contractSchema;
