const Joi = require("joi");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

const timestampSchema = Joi.alternatives().try(
  Joi.date().iso(),
  Joi.number().integer(),
  Joi.object({
    _seconds: Joi.number().integer().required(),
    _nanoseconds: Joi.number().integer().min(0).max(999999999).required(),
  })
);

const userSchema = Joi.object({
  name: Joi.string().required(),
  id: Joi.string().required(),
  time: timestampSchema.optional(),
});

// Schema for inventory items consumed in the sale
const inventoryItemSchema = Joi.object({
  itemName: Joi.string().max(100).required(),
  itemCode: Joi.string().max(50).required(),
  itemType: Joi.string().valid("bought", "made", "recipe").required(),
  recipeUom: Joi.string().required(),
  cost: Joi.number().min(0).default(0),
  requiredQty: Joi.number().min(0).required(),
});

const consumptionTrackingSchema = Joi.object({
  id: Joi.string(),
  tenantId: Joi.string().required(),

  // Sale reference fields
  saleReferenceId: Joi.string().required(),
  accountId: Joi.string().required(),
  storeId: Joi.string().required(),
  ticketNo: Joi.string().required(),
  tableNo: Joi.string().allow(null).optional(),
  floorNo: Joi.string().allow(null).optional(),

  // POS menu item fields
  posMenuItemName: Joi.string().required(),
  posItemCode: Joi.string().required(),
  servingSizeId: Joi.string().allow(null).optional(),
  servingSizeName: Joi.string().allow(null).optional(),
  soldQuantity: Joi.number().min(0).required(),
  posItemTotalAmount: Joi.number().min(0).optional(),

  // Work area and location
  workArea: Joi.string().allow(null).optional(),
  workAreaId: Joi.string().allow(null).optional(),
  location: Joi.string().allow(null).optional(),
  locationId: Joi.string().allow(null).optional(),

  // Inventory item details
  invItemName: Joi.string().optional(),
  invItemCode: Joi.string().optional(),
  invItems: Joi.array().items(inventoryItemSchema).default([]),

  // Status tracking
  status: Joi.string().valid("success", "error").optional(),
  statusSummary: Joi.string().optional(),

  // Timestamps
  createdAt: Joi.date().default(() => FD.now()),
  updatedAt: Joi.date().default(() => FD.now()),
});

module.exports = consumptionTrackingSchema;
