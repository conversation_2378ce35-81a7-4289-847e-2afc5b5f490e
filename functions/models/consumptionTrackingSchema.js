const Joi = require("joi");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

const timestampSchema = Joi.alternatives().try(
  Joi.date().iso(),
  Joi.number().integer(),
  Joi.object({
    _seconds: Joi.number().integer().required(),
    _nanoseconds: Joi.number().integer().min(0).max(999999999).required(),
  })
);

const userSchema = Joi.object({
  name: Joi.string().required(),
  id: Joi.string().required(),
  time: timestampSchema.optional(),
});

// Schema for inventory items consumed in the sale
const inventoryItemSchema = Joi.object({
  itemName: Joi.string().max(100).required(),
  itemCode: Joi.string().max(50).required(),
  itemType: Joi.string().valid("bought", "made", "recipe").required(),
  recipeUom: Joi.string().required(),
  cost: Joi.number().min(0).default(0),
  requiredQty: Joi.number().min(0).required(),
});

const consumptionTrackingSchema = Joi.object({
  id: Joi.string(),
  tenantId: Joi.string().required(),
  locationId: Joi.string().required(),
  locationName: Joi.string().required(),
  inventoryLocationId: Joi.string().required(),
  inventoryLocationName: Joi.string().required(),
  consumptionTrackingNumber: Joi.string().optional(),
  consumptionDate: timestampSchema.required(),
  consumptionType: Joi.string()
    .valid("PRODUCTION", "SALES", "WASTAGE", "SAMPLING", "INTERNAL_USE", "OTHER")
    .default("PRODUCTION"),
  consumptionReason: Joi.string().max(500).optional(),
  totalValue: Joi.number().min(0).optional(),
  items: Joi.array()
    .items(consumedItemSchema)
    .min(1)
    .required()
    .messages({ "array.min": "At Least One Item is Required" }),
  status: Joi.string()
    .valid("DRAFT", "CONFIRMED", "CANCELLED")
    .default("DRAFT"),
  remarks: Joi.string().max(1000).optional(),
  attachments: Joi.array().items(Joi.string()).optional(),
  createdBy: userSchema.optional(),
  confirmedBy: userSchema.optional(),
  cancelledBy: userSchema.optional(),
  createdAt: Joi.date().default(() => FD.now()),
  updatedAt: Joi.date().default(() => FD.now()),
});

module.exports = consumptionTrackingSchema;
