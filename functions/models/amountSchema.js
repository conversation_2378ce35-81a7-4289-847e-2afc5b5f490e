const Joi = require("joi");

const amountSchema = {
  grossAmount: Joi.number().precision(2).default(0),
  totalDiscount: Joi.number().precision(2).required(),
  netAmount: Joi.number().precision(2).default(0),
  charges: Joi.array().items(Joi.any()),
  totalChargeAmount: Joi.number().precision(2).default(0),
  taxes: Joi.array().items(Joi.any()),
  totalTaxAmount: Joi.number().precision(2).default(0),
  totalAmount: Joi.number().precision(2).required(),
  totalCess: Joi.number().precision(2).default(0),
  totalFocAmount: Joi.number().precision(2).default(0),
  taxRate: Joi.number().precision(2).default(0),
  roundoff: Joi.number().precision(2).default(0),
};

module.exports = { amountSchema };
