const Joi = require("joi");

const reportGroupSchema = Joi.object({
  id: Joi.string().optional(),
  name: Joi.string().required(),
  tenantId: Joi.string().required(),
  workarea: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        name: Joi.string().required()
      })
    )
    .required(),

  category: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        name: Joi.string().required()
      })
    )
    .required(),

  department: Joi.array().items(Joi.string()).required(),

  activeStatus: Joi.boolean().default(true)
});

module.exports = reportGroupSchema;
