const Joi = require("joi");

const tenantSchema = Joi.object({
  id: Joi.string().optional(),
  name: Joi.string().required(),
  emailId: Joi.string()
    .email()
    .required()
    .messages({ "string.pattern.base": "E-mail Must be Valid" }),
  isAdmin: Joi.boolean().default(true),
  // posId: Joi.string().required(), // backoffice tenant id
  activeStatus: Joi.boolean().default(true),
  createdAt: Joi.string().isoDate().optional(),
  nameNormalized: Joi.string().max(100).optional(),
  countryId: Joi.number().optional(),
  timeZone: Joi.string().optional(),
  settings: Joi.object({
    autoReceive: Joi.boolean().default(true),
    prApproval: Joi.boolean().default(false),
    poApproval: Joi.boolean().default(false),
    monthEndClosing: Joi.boolean().default(false),
    currentMonth: Joi.string().allow("", null).optional(),
  }).optional(),
});

module.exports = tenantSchema;
