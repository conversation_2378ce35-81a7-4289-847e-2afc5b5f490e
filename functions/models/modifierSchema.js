const Joi = require("joi");

const servingLevelSchema = Joi.object({
  servingSizeId: Joi.string().required(),
  servingSizeName: Joi.string().required(),
  qty: Joi.number().allow("").optional(),
  recipeUnit: Joi.string().allow("").optional(),
});

const modifierSchema = Joi.object({
  id: Joi.string().optional(),
  account: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
    posId: Joi.string().required(),
  }).required(),
  tenantId: Joi.string().required(),
  itemName: Joi.string().required(),
  itemCode: Joi.string().optional(),
  itemType: Joi.string().optional(),
  item: Joi.string().allow("").optional(),
  code: Joi.string().allow("").optional(),
  activeStatus: Joi.boolean().default(true),
  posId: Joi.string().required(),
  servingLevels: Joi.array().items(servingLevelSchema).optional(),
  items: Joi.any().optional(), // validate
  tags: Joi.any().optional(), // validate
});

module.exports = modifierSchema;
