{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "cross-env NODE_ENV=staging firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy:staging": "cross-env NODE_ENV=staging firebase deploy --only functions", "deploy:production": "cross-env NODE_ENV=production firebase deploy --only functions", "logs": "firebase functions:log", "deploy:query": "firebase deploy --only firestore:indexes"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"axios": "^1.11.0", "csv-parser": "^3.2.0", "dayjs": "^1.11.19", "dinero.js": "^1.9.1", "dotenv-flow": "^4.1.0", "exceljs": "^4.4.0", "express-multipart-file-parser": "^0.1.2", "firebase-admin": "^12.6.0", "firebase-functions": "^6.4.0", "fs": "^0.0.1-security", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "lodash.clonedeep": "^4.5.0", "module-alias": "^2.2.3", "multer": "^2.0.1", "nodemailer": "^7.0.6", "number-to-words": "^1.2.4", "pdfmake": "^0.2.20", "stream": "^0.0.3", "xlsx": "^0.18.5"}, "devDependencies": {"cross-env": "^10.0.0", "firebase-functions-test": "^3.1.0"}, "_moduleAliases": {"@": "."}, "private": true}