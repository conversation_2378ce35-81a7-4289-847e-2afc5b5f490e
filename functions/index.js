// Load environment variables from .env files using dotenv-flow
require("dotenv-flow").config();
require("module-alias/register");

const { onRequest } = require("firebase-functions/v2/https");
const { onSchedule } = require("firebase-functions/v2/scheduler");
const { initializeApp, cert } = require("firebase-admin/app");
const { firebaseConfig, FIREBASE_STORAGE_BUCKET } = require("@/permission"); // Import service account or firebase config credentials
const { getFirestore } = require("firebase-admin/firestore");

// Initialize Firebase Admin with service account credentials
// cert(firebaseConfig) expects the service account key object
initializeApp({
  credential: cert(firebaseConfig),
  storageBucket: FIREBASE_STORAGE_BUCKET
});

const db = getFirestore();
// Import the main Express app (all routes are managed inside routes/index.js)
const app = require("@/routes/index.js");

// Export the Express app as a Firebase Cloud Function
// - Deployed to asia-south1 region
// - Exposed as an HTTPS endpoint
exports.app = onRequest(
  {
    region: "asia-south1",
    memory: "1GiB", // or "2GiB" if needed
    timeoutSeconds: 180 // maximum allowed in v2
  },
  app
);

// ----------------------
// Scheduler Function
// ----------------------

exports.schedulerLogJob = onSchedule(
  {
    schedule: "*/5 * * * *", // every 3 minutes
    timeZone: "Asia/Kolkata",
    region: "asia-south1"
  },
  async () => {
    try {
      const logRef = await db.collection("scheduler_logs").add({
        jobName: "schedulerLogJob",
        source: "scheduler",
        status: "success",
        executedAt: new Date()
      });

      console.log("Scheduler log written:", logRef.id);
    } catch (error) {
      console.error("Scheduler job failed:", error);

      await db.collection("scheduler_logs").add({
        jobName: "schedulerLogJob",
        source: "scheduler",
        status: "failed",
        error: error.message,
        executedAt: new Date()
      });
    }
  }
);
