// dateHelper.js
const { Timestamp } = require("firebase-admin/firestore");
const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc.js");
const timezone = require("dayjs/plugin/timezone.js");

dayjs.extend(utc);
dayjs.extend(timezone);

// ------------------------------
// ⚙️ Configuration
// ------------------------------
const DEFAULT_TZ = "Asia/Kolkata";

const DATE_FORMAT = {
  WITH_TIME: "DD MMM YYYY HH:mm", // e.g. 01 Nov 2025 18:00
  DATE_ONLY: "DD MMM YYYY", // e.g. 01 Nov 2025
  TIME_ONLY: "HH:mm", // e.g. 18:00
};

// ------------------------------
// 🧭 Time Option Constants
// ------------------------------
const TIME_OPTION = {
  NOW: "now",
  START: "start",
  END: "end",
};

// ------------------------------
// 🔒 Internal Utility
// ------------------------------
/**
 * Internal helper that adjusts a date based on option.
 *
 * @param {dayjs.Dayjs} sourceDate - input dayjs date in DEFAULT_TZ
 * @param {"now" | "start" | "end" | undefined} option
 * @returns {dayjs.Dayjs} adjusted dayjs instance
 */
function _getDayjsForOption(sourceDate, option) {
  switch (option) {
    case TIME_OPTION.START:
      return sourceDate.startOf("day");
    case TIME_OPTION.END:
      return sourceDate.endOf("day");
    case TIME_OPTION.NOW:
    default:
      return sourceDate; // default = current time
  }
}

// ------------------------------
// 🧩 Firestore Date Helper
// ------------------------------
const FirestoreDateHelper = {
  /**
   * 🕓 now(option)
   * Returns a Firestore Timestamp for the current time, start, or end of today.
   *
   * @param {"now" | "start" | "end" | undefined} option
   * - undefined or "now" → current time
   * - "start" → start of today
   * - "end" → end of today
   *
   * Usage:
   *   FirestoreDateHelper.now()
   *   // → 2025-11-01T12:30:00.000Z (current time IST)
   *
   *   FirestoreDateHelper.now(TIME_OPTION.START)
   *   // → 2025-11-01T00:00:00.000Z
   *
   *   FirestoreDateHelper.now(TIME_OPTION.END)
   *   // → 2025-11-01T23:59:59.999Z
   */
  now(option = undefined) {
    const d = _getDayjsForOption(dayjs().tz(DEFAULT_TZ), option);
    return Timestamp.fromDate(d.utc().toDate());
  },

  /**
   * 🔄 toFirestore(jsDate, option)
   * Converts a JS Date → Firestore Timestamp.
   *
   * @param {Date|string|number} jsDate - JS Date, timestamp, or ISO string
   * @param {"now" | "start" | "end" | undefined} option
   * - undefined or "now" → keep exact time
   * - "start" → start of that day
   * - "end" → end of that day
   *
   * Usage:
   *   FirestoreDateHelper.toFirestore(new Date())
   *   // → 2025-11-01T12:30:00.000Z
   *
   *   FirestoreDateHelper.toFirestore("2025-11-01", TIME_OPTION.START)
   *   // → 2025-11-01T00:00:00.000Z
   *
   *   FirestoreDateHelper.toFirestore("2025-11-01", TIME_OPTION.END)
   *   // → 2025-11-01T23:59:59.999Z
   */
  toFirestore(jsDate, option = undefined) {
    if (!jsDate) return null;
    const d = _getDayjsForOption(dayjs(jsDate).tz(DEFAULT_TZ), option);
    return Timestamp.fromDate(d.utc().toDate());
  },

  addDays(firestoreTimestamp, days) {
    return Timestamp.fromDate(
      dayjs(firestoreTimestamp.toDate()).add(days, "day").toDate()
    );
  },

  subtractDays(firestoreTimestamp, days) {
    return Timestamp.fromDate(
      dayjs(firestoreTimestamp.toDate()).subtract(days, "day").toDate()
    );
  },

  compareTimestamps(t1, t2) {
    return t1.toMillis() - t2.toMillis();
  },

  /**
   * 🔁 toJSDate(firestoreTimestamp)
   * Converts Firestore Timestamp → JS Date.
   *
   * Usage:
   *   FirestoreDateHelper.toJSDate(doc.created_at)
   *   // → Sat Nov 01 2025 18:00:00 GMT+0530 (India Standard Time)
   */
  toJSDate(firestoreTimestamp) {
    return firestoreTimestamp?.toDate() ?? null;
  },

  /**
   * 🗓️ toFormattedDate(firestoreTimestamp, format)
   * Converts Firestore Timestamp → formatted string (for UI display).
   *
   * Usage:
   *   FirestoreDateHelper.toFormattedDate(doc.created_at)
   *   // → "01-Nov-2025 18:00"
   *
   *   FirestoreDateHelper.toFormattedDate(doc.created_at, DATE_FORMAT.DATE_ONLY)
   *   // → "01-Nov-2025"
   */
  toFormattedDate(
    firestoreTimestamp,
    format = DATE_FORMAT.WITH_TIME,
    tz = DEFAULT_TZ
  ) {
    if (!firestoreTimestamp) return "";
    return dayjs(firestoreTimestamp.toDate()).tz(tz).format(format);
  },
};

// ------------------------------
// 💅 General Formatter (Non-Firestore)
// ------------------------------
/**
 * 🗓️ formatJSDate(jsDate, format)
 * Converts a JS Date → formatted string for UI or exports.
 *
 * Usage:
 *   formatJSDate(new Date())
 *   // → "01-Nov-2025 18:00"
 *
 *   formatJSDate(new Date(), DATE_FORMAT.DATE_ONLY)
 *   // → "01-Nov-2025"
 */
function formatJSDate(jsDate, format = DATE_FORMAT.WITH_TIME, tz = DEFAULT_TZ) {
  if (!jsDate) return "";
  return dayjs(jsDate).tz(tz).format(format);
}

function dateStringToJsDate(dateStr) {
  // Expecting format: DD-MMM-YYYY (e.g., 31-JAN-2025)
  const [day, monStr, year] = dateStr.split("-");

  const monthMap = {
    JAN: 0,
    FEB: 1,
    MAR: 2,
    APR: 3,
    MAY: 4,
    JUN: 5,
    JUL: 6,
    AUG: 7,
    SEP: 8,
    OCT: 9,
    NOV: 10,
    DEC: 11,
  };

  const month = monthMap[monStr.toUpperCase()];
  if (month === undefined) {
    throw new Error(`Invalid month: ${monStr}`);
  }

  // Create date at midnight UTC to avoid timezone shifts
  return new Date(Date.UTC(Number(year), month, Number(day)));
}

module.exports = {
  DEFAULT_TZ,
  DATE_FORMAT,
  TIME_OPTION,
  FirestoreDateHelper,
  formatJSDate,
  dateStringToJsDate,
};
