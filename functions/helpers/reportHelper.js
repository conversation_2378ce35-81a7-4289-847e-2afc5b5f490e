// helpers/reportHelper.js

const {
  FirestoreDateHelper: FD,
  TIME_OPTION
} = require("@/helpers/dateHelper");
const { ResultType } = require("./render");

/**
 * Validate and prepare report filters.
 * Ensures required fields and adds Firestore-ready date values.
 *
 * @param {string} tenantId
 * @param {object} filters
 * @returns {object} validated and prepared filters
 */
const validateAndPrepareFilters = (tenantId, filters) => {
  if (!tenantId) throw new Error("Tenant ID is required.");
  if (!filters) throw new Error("invalid filters.");

  const { fromDate, toDate } = filters;

  if (!fromDate || !toDate) {
    throw new Error("Date range (dateFrom and dateTo) is required.");
  }

  // Add Firestore-ready dates
  // ... add customizations here
  const prepared = {
    ...filters,
    _fsFromDate: FD.toFirestore(fromDate, TIME_OPTION.START),
    _fsToDate: FD.toFirestore(toDate, TIME_OPTION.END)
  };

  return prepared;
};

/**
 * Determines whether the given filters require ledger data.
 * Ledger data is used if any of the following filter keys are present:
 * - categories
 * - subcategories
 * - inventoryItems
 *
 * @param {object} filters - The filters object to check.
 * @returns {boolean} True if ledger data is required, false otherwise.
 */
function requiresLedgerData(filters = {}) {
  if (typeof filters !== "object" || filters === null) return false;

  return ["categories", "subcategories", "inventoryItems"].some(
    (key) => Array.isArray(filters[key]) && filters[key].length > 0
  );
}

function filterItem(filters = {}, item) {
  const { categories, subCategories, inventoryItems } = filters;
  if (
    (categories.length && !categories.includes(item.categoryId)) ||
    (subCategories.length && !subCategories.includes(item.subcategoryId)) ||
    (inventoryItems.length && !inventoryItems.includes(item.itemId))
  ) {
    return false; // skip non-matching items
  }
  return true;
}

/**
 * Construct final report columns.
 *
 * Rules:
 * 1. If no predefined columns, return [].
 * 2. Merge UI overrides (enable, ordinal, etc.).
 * 3. Always include:
 *      - Mandatory columns
 *      - Or explicitly enabled columns from UI
 * 4. Expand destructible columns (e.g. `taxes[]` → `taxes_CGST`, `taxes_SGST`)
 *      - Uses meta = { taxes: { CGST: "CGST", SGST: "SGST" } }
 *      - Expanded columns get fractional ordinals (e.g., 4.1, 4.2)
 * 5. Sort by ordinal and normalize numbering (1, 2, 3, ...)
 *
 * @param {Array<Object>} predefinedColumns - Static base column definitions.
 * @param {Array<Object>} [uiColumns=[]] - User overrides for enable/order.
 * @param {Object} [meta={}] - Metadata used for destructible fields.
 * @returns {Array<Object>} Final ordered, expanded, merged column definitions.
 */
function constructColumns(
  predefinedColumns = [],
  uiColumns = [],
  meta = {},
  splitBy,
  resultType
) {
  if (!Array.isArray(predefinedColumns) || predefinedColumns.length === 0)
    return [];

  let splitByColumn = null;
  const final = [];
  const override = Array.isArray(uiColumns) && uiColumns.length > 0;

  for (const base of predefinedColumns) {
    const col = { ...base };
    col.title = col.title || col.header;

    // Handle split-by column always appear first
    if (col.key === splitBy) {
      splitByColumn = col;
      continue;
    }

    const ui = override ? uiColumns.find((u) => u.key === col.key) : null;

    // Initialize enable flag
    col.enable = col.mandatory ? true : ui?.enable ?? col.enable;

    // Override ordinal if provided
    if (ui?.ordinal) col.ordinal = ui.ordinal;

    if (!col.enable) continue;

    // Handle destructible columns
    const destructKey = col.key;
    if (col.destruct && meta && meta[destructKey]) {
      const entries = Object.entries(meta[destructKey]);
      entries.forEach(([key, header], i) => {
        if (col.subKeys && Array.isArray(col.subKeys)) {
          col.subKeys.forEach((subKey) => {
            final.push({
              ...col,
              key: `${destructKey}_${key}_${subKey}`,
              header: `${header} ${subKey}`,
              title: `${header} ${subKey}`,
              ordinal: parseFloat(`${col.ordinal}.${i + 1}`),
              destruct: undefined,
              subKeys: col.subKeys
            });
          });
        } else {
          final.push({
            ...col,
            key: `${destructKey}_${key}`,
            header,
            title: header,
            ordinal: parseFloat(`${col.ordinal}.${i + 1}`),
            destruct: undefined
          });
        }
      });
      continue;
    }

    final.push(col);
  }

  // Sort by ordinal
  final.sort((a, b) => (a.ordinal || 999) - (b.ordinal || 999));

  if (splitByColumn) {
    final.unshift(splitByColumn);
    if (resultType === ResultType.JSON) {
      final.unshift({
        width: 1,
        key: "data-table-expand",
        align: "end",
        isExpanded: true
      });
    }
  }
  // Normalize ordinals (1..N)
  final.forEach((c, i) => (c.ordinal = i + 1));

  return final;
}

const validateIdentityFilters = (identity, filters = {}) => {
  if (!identity) throw new Error("Invalid identity");
  if (!filters || typeof filters !== "object") {
    throw new Error("Invalid filters");
  }

  const {
    isAdmin = false,
    locations: allowedLocations = [],
    inventoryLocations: allowedInventoryLocations = []
  } = identity;

  // Admin → no restriction
  if (isAdmin) {
    return filters;
  }

  /* ---------- LOCATION VALIDATION ---------- */

  if ("locations" in filters) {
    const { locations } = filters;
    if (Array.isArray(allowedLocations) && allowedLocations.length > 0) {
      // Identity is restricted
      if (Array.isArray(locations) && locations.length > 0) {
        const invalid = locations.filter(
          (loc) => !allowedLocations.includes(loc)
        );

        if (invalid.length > 0) {
          throw new Error(`Unauthorized locations: ${invalid.join(", ")}`);
        }
      } else {
        // No filter → enforce identity scope
        filters.locations = allowedLocations;
      }
    }
  }
  // else → [] means ALL access → do nothing

  /* ------ INVENTORY LOCATION VALIDATION ----- */

  const inventoryLocationScopedFields = ["inventoryLocations", "requester", "issuer"];

  inventoryLocationScopedFields.forEach((field) => {
    if (field in filters) {
      const value = filters[field];

      if (
        Array.isArray(allowedInventoryLocations) &&
        allowedInventoryLocations.length > 0
      ) {
        if (Array.isArray(value) && value.length > 0) {
          const invalid = value.filter(
            (v) => !allowedInventoryLocations.includes(v)
          );

          if (invalid.length > 0) {
            throw new Error(`Unauthorized ${field}: ${invalid.join(", ")}`);
          }
        } else {
          // Enforce identity scope
          filters[field] = allowedInventoryLocations;
        }
      }
    }
  });

  return filters;
};

module.exports = {
  validateAndPrepareFilters,
  requiresLedgerData,
  filterItem,
  constructColumns,
  validateIdentityFilters
};
