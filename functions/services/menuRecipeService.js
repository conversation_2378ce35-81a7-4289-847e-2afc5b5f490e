const admin = require("firebase-admin");
const db = admin.firestore();
const schema = require("@/models/menuRecipeSchema");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { handleValidation } = require("@/utils/validation");
const {
  saveMenuRecipe,
  getAllMenuRecipes,
  getById,
  updateById,
} = require("@/repositories/menuRecipeRepo");
const { getNextMenuRecipeId } = require("./counterService");
const { LedgerTypes } = require("@/defs/ledgerDefs");
const { creditStock,debitStock } = require("./stockService");
const { getReceipeById,getRecordsByFloorId, getMenuItemById } = require("@/repositories/receipeRepo");
const { getInventoryItemStocks,calculateAggregateQty } = require("@/repositories/stockRepo");
const { debitStockByRecipeQty } = require("@/services/stockOperations");

const createMenuRecipe = async (data) => {
  try {
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return;

    const recipeSplits = [];

    for (const item of validatedData.items) {
      console.log("🚀 ~ createMenuRecipe ~ item:", item)
      const ingredientDetails = await prepareMenuRecipeSplitup(
        validatedData.tenantId,
        item.recipeId,
        validatedData.workAreaId
      );

      recipeSplits.push({
        ...ingredientDetails,
        preparedQty: item.quantity, 
      });
    }

    const ingredientData = validateIngredientStock(recipeSplits);    
    const menuRecipeNumber = await getNextMenuRecipeId(validatedData.tenantId);
    const result = await processTransactions(validatedData, ingredientData, menuRecipeNumber);

    return result;

  } catch (error) {
    console.error("Error in createMenuRecipe:", error);
    throw new Error(error.message);
  }
};

const validateIngredientStock = (ingredientData) => {
  const ingredientMap = {};

  for (const item of ingredientData) {
    const recipeBaseQty = item.quantity;
    const preparedQty = item.preparedQty;

    for (const ing of item.ingredients) {
      const requiredQuantity = (ing.recipeQty / recipeBaseQty) * preparedQty;

      if (!ingredientMap[ing.itemId]) {
        ingredientMap[ing.itemId] = {
          itemId: ing.itemId,
          itemName: ing.itemName,
          inStock: ing.inStock,
          totalRecipeQty: 0,
        };
      }

      ingredientMap[ing.itemId].totalRecipeQty += requiredQuantity;
    }
  }

  const ingredientArray = Object.values(ingredientMap);

  const insufficientItems = ingredientArray.filter(
    item => item.inStock < item.totalRecipeQty
  );

  if (insufficientItems.length) {
    throw new Error(
      `Insufficient stock for: ${insufficientItems
        .map(i => i.itemName)
        .join(", ")}`
    );
  }

  return ingredientArray;
};

const processTransactions = async (validatedData,ingredientData,menuRecipeNumber) => {
  return await db.runTransaction(async (trans) => {

    for (const ing of ingredientData) {      
      // DEBIT STOCK
      await debitStockByRecipe(
        {
          itemId: ing.itemId,
          qtyInRecipeUOM: ing.totalRecipeQty,
          inventoryLocationId: validatedData.workAreaId,
        },
        trans
      );
    }

    for (const item of validatedData.items) {
      // CREDIT STOCK
      let qty; 
      if (item.recipeUOM === item.purchaseUOM) {
        qty = item.quantity;
      } else {
        if (item.purchaseUOM === "l" || item.purchaseUOM === "kg") {
          qty = item.quantity / 1000;
        } else if (item.purchaseUOM === "nos") {
          qty = item.quantity;
        }
      }
      const pkg = {
        name: item.purchaseUOM,
        id: 'default'
      };
      await creditStock(
        {
          ledgerType: LedgerTypes.MENU_RECIPE_CREDIT,
          tenantId: validatedData.tenantId,
          locationId: validatedData.locationId,
          locationName: validatedData.locationName,
          inventoryLocationId: validatedData.workAreaId,
          inventoryLocationName: validatedData.workAreaName,
          itemId: item.itemId,
          itemCode: item.itemCode,
          itemName: item.itemName,
          qty,
          pkgUOM: item.purchaseUOM,
          unitCost: item.unitCost || 0,
          totalCost: (item.unitCost || 0) * qty,
          expiryDate: item.expiryDate || null,
          grnMeta: null,
          categoryId: item.categoryId,
          subcategoryId: item.subcategoryId,
          categoryName: item.categoryName,
          subcategoryName: item.subcategoryName,
          pkg,
          remarks: item.remarks || null,
        },
        trans
      );
    }

    return await saveMenuRecipe(
      { ...validatedData, menuRecipeNumber },
      trans
    );
  });
};

const getMenuRecipes = async (filters) => {
  const menuRecipes = await getAllMenuRecipes(filters);

  const result = menuRecipes.map((s) => {
    return {
      menuRecipeNumber: s.menuRecipeNumber,
      locationName: s.locationName,
      workAreaName: s.workAreaName,
      id: s.id,
      requestedBy: s.requestedBy.name,
      requestedAt: FD.toFormattedDate(s.requestedBy.time),
    };
  });
  return result;
};

const getMenuRecipeById = async (id) => {
  try {
    const data = await getById(id);
    if (!data) return null;
    return {
      ...data,
      requestedBy: {
        ...data.requestedBy,
        time: FD.toFormattedDate(data.requestedBy.time),
      },
    };
  } catch (error) {
    throw Error(err.message);
  }
};

/**
 * Recursively flatten nested recipes and aggregate quantities
 * Stops at 'bought' or 'made' items (base items)
 * Expands 'recipe' or 'subRecipe' items (nested recipes)
 */
const flattenRecipeIngredients = async (
  tenantId,
  recipeId,
  multiplier = 1,
  itemsMap = {},
  visitedRecipes = new Set()
) => {
  // Prevent infinite recursion
  if (visitedRecipes.has(recipeId)) {
    console.warn(`Circular recipe dependency detected: ${recipeId}`);
    return itemsMap;
  }
  
  visitedRecipes.add(recipeId);

  const recipeData = await getReceipeById(tenantId, recipeId);
  
  if (!recipeData || !Array.isArray(recipeData.ingredients)) {
    return itemsMap;
  }

  // Process each ingredient
  for (const ingredient of recipeData.ingredients) {
    const consumptionQty = ingredient.consumptionQuantity || 0;

    // Check if this ingredient is a nested recipe (recipe or subRecipe)
    if (ingredient.itemType === 'recipe' || ingredient.itemType === 'subRecipe') {
      // Fetch the nested recipe to get its quantity
      const nestedRecipe = await getReceipeById(tenantId, ingredient.itemId);
      
      if (nestedRecipe) {
        // Calculate how much of the nested recipe we need
        const neededAmount = consumptionQty * multiplier;
        // Calculate multiplier for nested recipe (how many times to make it)
        const nestedMultiplier = neededAmount / (nestedRecipe.quantity || 1);
        
        // Recursive call for nested recipe
        await flattenRecipeIngredients(
          tenantId,
          ingredient.itemId,
          nestedMultiplier,
          itemsMap,
          new Set(visitedRecipes) // Copy to allow same recipe in different branches
        );
      }
    } else if (ingredient.itemType === 'bought' || ingredient.itemType === 'made') {
      // It's a base item (bought or made) - accumulate quantity
      const totalQty = consumptionQty * multiplier;
      const itemId = ingredient.itemId;
      
      if (!itemsMap[itemId]) {
        itemsMap[itemId] = {
          itemId: itemId,
          itemName: ingredient.itemName,
          itemCode: ingredient.itemCode,
          recipeQty: 0,
          unit: ingredient.recipeUnit?.symbol || '',
          itemType: ingredient.itemType
        };
      }
      
      itemsMap[itemId].recipeQty += totalQty;
    }
  }

  return itemsMap;
};

/**
 * Main function to prepare menu recipe splitup
 */
const prepareMenuRecipeSplitup = async (tenantId, recipeId, inventoryLocationId) => {
  try {
    if (!tenantId || !recipeId || !inventoryLocationId) {
      throw new Error("tenantId, recipeId, and inventoryLocationId are required");
    }

    const recipeData = await getReceipeById(tenantId, recipeId);
    
    if (!recipeData) {
      return {
        tenantId,
        ingredients: [],
        quantity: 0,
        error: "Recipe not found"
      };
    }

    // Step 1: Flatten all nested recipes with multiplier = 1 (make 1 batch)
    const itemsMap = await flattenRecipeIngredients(
      tenantId,
      recipeId,
      1 // Start with multiplier 1 (make one batch of the recipe)
    );

    // Step 2: Convert map to array format for calculateAggregateQty
    const flattenedItems = Object.values(itemsMap);

    // Step 3: Calculate stock availability
    const ingredientsWithStock = flattenedItems.length > 0
      ? await calculateAggregateQty(inventoryLocationId, flattenedItems)
      : [];

    return {
      tenantId,
      recipeId,
      recipeName: recipeData.name,
      quantity: recipeData.quantity,
      recipeUnit: recipeData.recipeUnit?.symbol || '',
      ingredients: ingredientsWithStock,
      totalUniqueItems: flattenedItems.length,
      recipeCost: recipeData.cost
    };
    
  } catch (error) {
    console.error("Error in prepareMenuRecipeSplitup:", {
      tenantId,
      recipeId,
      inventoryLocationId,
      error: error.message,
      stack: error.stack
    });
    throw new Error(`Failed to prepare recipe splitup: ${error.message}`);
  }
};

const updateMenuRecipe = async (id, payload) => {
  try {
    const data = handleValidation(payload, schema);
    if (!data) return;
    const result = await updateById(id, data);
    return result;
  } catch (error) {
    throw Error(err.message);
  }
};

const debitStockByRecipe = async (payload, t) => {
  return await debitStockByRecipeQty(payload, t, calculateAggregateQty, debitStock);
};

const deductSalesData = async (tenantId) => {
  // pull sales 
  const sales = [{
    "sale_reference_id": ********,
    "account_id": 9,
    "store_id": 18,
    "ticket_no": "29591",
    "table_no": "6666",
    "floor_no": 527,
    "guest_count": 1,
    "sale_type": 1,
    "service_type": 3,
    "owner_id": 12722,
    "open_by": 12722,
    "close_by": 825,
    "node": "A885",
    "business_date": "19-Jan-2026",
    "open_date": "19-Jan-2026 13:04",
    "close_date": "19-Jan-2026 14:01",
    "last_updated_date": "19-Jan-2026 08:31",
    "status": 6,
    "customer_name": "Walk-in",
    "customer_phone_number": "",
    "service_charge_exemption": false,
    "gross_amount": 56800,
    "charge_amount": 0,
    "tax_amount": 28400,
    "discount_amount": 0,
    "round_off": -40,
    "total_amount": 59600,
    "nc_amount": 0,
    "gross_amount_fmt": "568.00",
    "charge_amount_fmt": "0.00",
    "tax_amount_fmt": "28.40",
    "discount_amount_fmt": "0.00",
    "round_off_fmt": "-0.40",
    "total_amount_fmt": "596.00",
    "nc_amount_fmt": "0.00",
    "tip_amount": 0,
    "tip_amount_fmt": "0.00",
    "items": [
      {
        "menu_item_id": 232798,
        "menu_item_name": "Tonic Water Can",
        "plu_code": "DIGI-102665",
        "ledger_category": {
          "id": 256,
          "name": "-OTHERS-"
        },
        "serving_size_id": 89,
        "serving_size_name": "EACH",
        "department": "NAB",
        "cuisines": "Beverages",
        "categories": "",
        "quantity": 1,
        "kot_sent_date": "19-Jan-2026 13:04:20",
        "kot_sent_date_gm": "19-01-2026 13:04:20",
        "kot_sent_by": 12722,
        "void_by": 0,
        "void_reason": "",
        "status": 6,
        "gross_amount": 19900,
        "charge_amount": 0,
        "tax_amount": 9950,
        "discount_amount": 0,
        "total_amount": 20895,
        "nc_amount": 0,
        "gross_amount_fmt": "199.00",
        "charge_amount_fmt": "0.00",
        "tax_amount_fmt": "9.95",
        "discount_amount_fmt": "0.00",
        "total_amount_fmt": "208.95",
        "nc_amount_fmt": "0.00",
        "discount": null,
        "modifier_receipe": null,
        "taxes": [
          {
            "tax_ID": 24,
            "tax_name": "SGST - Food & Beverages",
            "total_tax_amount": 4975,
            "total_tax_amount_fmt": "4.98",
            "inclusive": false,
            "percentage": 2.5
          },
          {
            "tax_ID": 22,
            "tax_name": "CGST - Food & Beverages",
            "total_tax_amount": 4975,
            "total_tax_amount_fmt": "4.98",
            "inclusive": false,
            "percentage": 2.5
          }
        ]
      }
    ],
    "taxes": [
      {
        "tax_ID": 22,
        "tax_name": "CGST - Food & Beverages",
        "total_tax_amount": 14200,
        "total_tax_amount_fmt": "14.20",
        "inclusive": false,
        "percentage": 2.5
      },
      {
        "tax_ID": 24,
        "tax_name": "SGST - Food & Beverages",
        "total_tax_amount": 14200,
        "total_tax_amount_fmt": "14.20",
        "inclusive": false,
        "percentage": 2.5
      }
    ],
    "payments": [
      {
        "payment_name": "Card",
        "payment_type": "Card",
        "amount": 59600,
        "amount_fmt": "596.00",
        "tip_amount": 0,
        "tip_amount_fmt": "0.00",
        "received_by": 15256,
        "drawer_id": "a0238c92-e418-4dbe-ab2b-e73939114ed2",
        "status": 6
      }
    ],
    "charges": [],
    "status_gm": "sales",
    "business_date_gm": "19-01-2026",
    "open_date_gm": "19-01-2026 13:04:00",
    "close_date_gm": "19-01-2026 14:01:00",
    "last_updated_date_gm": "19-01-2026 08:31:51"
  }];

  for (const element of sales) {
    const workArea = await getRecordsByFloorId(tenantId, element.floor_no);
    console.log("🚀 ~ deductSalesData ~ workArea:472", workArea);
    
    if (!workArea) {
      console.log("🚀 ~ deductSalesData ~ workArea:475", "floor not found");
      element['error'] = "floor not found";
      continue;
    }
    
    for (const item of element.items) {
      const itemCode = item['plu_code'];
      const requiredRecipe = await getMenuItemById(tenantId, itemCode);
      console.log(requiredRecipe, "requiredRecipe");
      
      if (!requiredRecipe) {
        console.log("🚀 ~ deductSalesData ~ requiredRecipe: 485", `menu item not found for ${itemCode}`);
        item['error'] = "menu item not found";
        element['error'] = "missing menu items";
        element['missingItems'] = element['missingItems'] ? [...element['missingItems'], item] : [item];
        continue;
      }
      
      if (requiredRecipe['linkingStatus'] == false) {
        console.log("🚀 ~ deductSalesData ~ requiredRecipe: 493", `menu item not linked for ${itemCode}`);
        item['error'] = "menu item not linked";
        element['error'] = "menu item not linked";
        element['nonLinkedItems'] = element['nonLinkedItems'] ? [...element['nonLinkedItems'], item] : [item];
        continue;
      }

      const reductionQty = requiredRecipe['servingLevels'].find(ele => ele.servingSizeName == item.serving_size_name);
      console.log("🚀 ~ deductSalesData ~ reductionQty: 502", reductionQty);
      
      if (!reductionQty) {
        console.log("🚀 ~ deductSalesData ~ reductionQty: 505", `serving size not found for ${item.serving_size_name}`);
        item['error'] = "serving size not found";
        continue;
      }

      // update code based on recipe linking  
      if (requiredRecipe['items']['itemType'] && ['bought', 'made'].includes(requiredRecipe['items']['itemType'])) {
        
        // DEBIT STOCK
        await debitStockByRecipe(
          {
            itemId: requiredRecipe['items']['itemType']['id'],
            qtyInRecipeUOM: reductionQty['qty'],
            inventoryLocationId: workArea,
          }
        );
      } else if (requiredRecipe['items']['recipeType'] == 'recipe') {
        const ingredientDetails = await prepareMenuRecipeSplitup(
          tenantId,
          requiredRecipe['items']['itemType']['id'],
          workArea
        );

        const recipeSplits = [{
          ...ingredientDetails,
          preparedQty: item.quantity, 
        }];

        const ingredientData = validateIngredientStock(recipeSplits);

        for (const ing of ingredientData) {      
          // DEBIT STOCK
          await debitStockByRecipe(
            {
              itemId: ing.itemId,
              qtyInRecipeUOM: ing.totalRecipeQty,
              inventoryLocationId: workArea,
            },
            trans
          );
        }
      }
    }
  }
  
  return sales;
};


module.exports = {
  createMenuRecipe,
  getMenuRecipes,
  getMenuRecipeById,
  updateMenuRecipe,
  prepareMenuRecipeSplitup,
  debitStockByRecipe,
  validateIngredientStock,
  deductSalesData
};
