// services/purchaseService.js
const admin = require("firebase-admin");
const db = admin.firestore();
const prSchema = require("@/models/purchaseRequestSchema");
const poSchema = require("@/models/purchaseOrderSchema");
const { handleValidation } = require("@/utils/validation");

const {
  getPOById,
  updatePO,
  getPurchaseOrders,
  updatePurchaseOrderById,
} = require("@/repositories/poRepo");
const { createGRN } = require("./stockTransactionService");
const {
  getPurchaseRequests,
  savePurchaseRequest,
  getPurchaseRequestById,
  updatePurchaseRequestById,
} = require("@/repositories/prRepo");
const { paiseToRupee, rupeeToPaise } = require("@/utils/money");
const { purchaseStatus } = require("@/defs/purchaseStatusDefs");
const {
  DATE_FORMAT,
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const {
  getNextPurchaseRequestId,
  getNextPurchaseOrderId,
} = require("@/services/counterService");
const { isApprovalNeeded } = require("@/services/tenantService");
const { savePurchaseOrder } = require("@/repositories/poRepo");
const { getVendorById } = require("@/repositories/vendorRepo");
const { validateInvoiceNumber } = require("@/repositories/grnRepo");
const {
  createTransferRequest,
  createDispatch,
  receiveTransfer,
} = require("@/services/transferService");
const { transferStatus } = require("@/defs/transferStatusDefs");
const cartHelper = require("@/helpers/cartHelper");
const {
  formatAmountToPaise,
  formatAmountToRupee,
} = require("@/helpers/amountFormat.js");

/**
 * Receive items against a PO
 * - Validates items against PO
 * - Updates PO items receivedQty & notes
 * - Persists GRN in Firestore
 * - Calls creditStock for each item to update stock & ledger
 * - Returns the full GRN document
 */
const receivePO = async (params) => {
  return db
    .runTransaction(async (t) => {
      const {
        poId,
        grnDate,
        invoiceDate,
        invoiceNumber,
        grnItems,
        receivedById,
        receivedByName,
        poOption,
        directIssueList,
        paymentTerms,
        poTerms,
        remarks = null,
        totalAmount,
        totalCess,
        totalChargeAmount,
        totalDiscount,
        totalFocAmount,
        totalTaxAmount,
        netAmount,
        grossAmount,
        taxes = [],
        charges = [],
      } = params;

      // Step 1: Fetch and validate PO
      const poData = await getPOById(poId, t);
      if (!poData) throw new Error("PO not found");

      // Step 2: Validate vendor invoice uniqueness
      await validateInvoiceNumber({
        vendorId: poData.vendor.id,
        poId,
        invoiceNumber,
        t,
      });

      // Step 3: Create GRN and update PO item details
      const { grn, updatedPO } = await createGRN(
        {
          poData,
          grnDate,
          invoiceDate,
          invoiceNumber,
          grnItems,
          receivedById,
          receivedByName,
          paymentTerms,
          poTerms,
          remarks,
          totalAmount,
          totalCess,
          totalChargeAmount,
          totalDiscount,
          totalFocAmount,
          totalTaxAmount,
          netAmount,
          grossAmount,
          taxes,
          charges,
        },
        t
      );

      // --- Helper to update status timeline ---
      const updateStatusTimeline = (status) => {
        updatedPO.statusTimeline.push({
          name: status,
          time: FD.now(),
          by: { name: receivedByName, id: receivedById },
        });
        return updatedPO.statusTimeline;
      };

      // Step 4: Apply PO status update logic
      let finalPOUpdate = {
        items: updatedPO.items,
        vendor: { ...poData.vendor, paymentTerms, poTerms },
        remarks,
      };

      if (poOption === 1) {
        // Partial
        finalPOUpdate.status = purchaseStatus.PARTIAL;
        finalPOUpdate.statusTimeline = updateStatusTimeline(
          purchaseStatus.PARTIAL
        );
      } else {
        // Completed
        finalPOUpdate.status = purchaseStatus.COMPLETED;
        finalPOUpdate.statusTimeline = updateStatusTimeline(
          purchaseStatus.COMPLETED
        );

        finalPOUpdate.invoiceDate = FD.toFirestore(
          invoiceDate,
          TIME_OPTION.START
        );
        finalPOUpdate.grnDate = FD.toFirestore(grnDate, TIME_OPTION.START);
        finalPOUpdate.invoiceNumber = invoiceNumber;
      }

      await updatePO(poId, finalPOUpdate, t);

      // Step 5 (outside transaction): Handle direct issue items
      // This must remain outside because it's not transactional
      return {
        grn,
        poData,
        directIssueList,
        receivedById,
        receivedByName,
      };
    })
    .then(async (result) => {
      // Perform non-transactional work AFTER commit
      const { grn, poData, directIssueList, receivedById, receivedByName } =
        result;

      if (directIssueList?.length) {
        await handleDirectIssueList({
          directIssueList,
          poData,
          receivedById,
          receivedByName,
          transferDate: params.grnDate,
        });
      }

      return {
        ...grn,
        totalAmount: paiseToRupee(grn.totalAmount),
      };
    });
};

const handleDirectIssueList = async ({
  directIssueList,
  poData,
  receivedById,
  receivedByName,
  transferDate,
}) => {
  for (const issue of directIssueList) {
    // Separate stockable and non-stockable items
    const stockableItems = issue.items.filter((item) => item.stockable);
    const nonStockableItems = issue.items.filter((item) => !item.stockable);

    // Process both types in parallel for better performance
    await Promise.all(
      [
        stockableItems.length > 0 &&
          processTransferFlow({
            issue,
            items: stockableItems,
            poData,
            receivedById,
            receivedByName,
            transferDate,
            stockableItems: true,
          }),
        nonStockableItems.length > 0 &&
          processTransferFlow({
            issue,
            items: nonStockableItems,
            poData,
            receivedById,
            receivedByName,
            transferDate,
            stockableItems: false,
          }),
      ].filter(Boolean)
    );
  }
};

/**
 * Helper function to process transfer flow for a set of items
 * Handles: Create Transfer -> Dispatch -> Receive
 */
const processTransferFlow = async ({
  issue,
  items,
  poData,
  receivedById,
  receivedByName,
  transferDate,
  stockableItems,
}) => {
  const tsNow = FD.now();

  // Step 1: Create Transfer Request
  const transferPayload = {
    tenantId: poData.tenantId,
    issuer: issue.issuer,
    requester: issue.requester,
    items: items.map((item) => ({
      ...item,
      requestedQuantity: item.requestedQuantity,
    })),
    requestedBy: {
      id: receivedById,
      name: receivedByName,
      time: tsNow,
    },
    transferDate,
    stockableItems,
  };

  const createdTransfer = await createTransferRequest(transferPayload, true);

  // Step 2: Create Dispatch Number
  const dispatchPayload = {
    id: createdTransfer.id,
    items: createdTransfer.items.map((item) => ({
      ordinal: item.ordinal,
      itemId: item.itemId,
      dispatchedQuantity: item.requestedQuantity,
      pkg: item.pkg,
    })),
    tenantId: createdTransfer.tenantId,
  };

  await createDispatch(
    dispatchPayload,
    createdTransfer,
    receivedById,
    receivedByName,
    true
  );
};

const aggregatePurchaseRequests = async (tenantId, filters) => {
  const purchaseRequests = await getPurchaseRequests(tenantId, filters);

  const result = purchaseRequests.map((pr) => {
    const vendors = pr.items.map((item) => item.vendor?.id);
    return {
      id: pr.id,
      prNumber: pr.prNumber,
      deliveryDate: FD.toFormattedDate(pr.deliveryDate, DATE_FORMAT.DATE_ONLY),
      status: pr.status,
      tenantId: pr.tenantId,
      vendor: [...new Set(vendors)].length > 1 ? "-" : pr.items[0].vendor?.name,
      createdAt: FD.toFormattedDate(pr.statusTimeline[0].time),
      totalAmount: paiseToRupee(pr.totalAmount),
      requestedBy: pr.requestedBy?.name,
      locationName: pr.location?.name,
    };
  });
  return result;
};

const aggregatePurchaseOrders = async (tenantId, filters) => {
  const purchaseOrders = await getPurchaseOrders(tenantId, filters);

  const result = purchaseOrders.map((po) => {
    return {
      id: po.id,
      poNumber: po.poNumber,
      deliveryDate: FD.toFormattedDate(po.deliveryDate, DATE_FORMAT.DATE_ONLY),
      status: po.status,
      tenantId: po.tenantId,
      createdAt: FD.toFormattedDate(po.statusTimeline[0].time),
      totalAmount: paiseToRupee(po.totalAmount),
      vendor: po.vendor?.name,
      requestedBy: po.requestedBy?.name,
      locationName: po.location?.name,
    };
  });
  return result;
};

const buildStatusTimeline = (data, isApprovalNeed) => {
  const timeline = [];
  const tsNow = FD.now();

  // Case 1: SUBMITTED
  if (data.status === purchaseStatus.SUBMITTED) {
    timeline.push({
      name: purchaseStatus.SUBMITTED,
      time: tsNow,
      by: data.requestedBy,
    });

    // Auto-approve if no approval needed
    if (!isApprovalNeed) {
      timeline.push({
        name: purchaseStatus.APPROVED,
        time: tsNow,
        by: data.requestedBy,
      });
      return {
        status: purchaseStatus.APPROVED,
        statusTimeline: timeline,
      };
    }

    return {
      status: purchaseStatus.SUBMITTED,
      statusTimeline: timeline,
    };
  }

  // Case 2: DRAFT
  if (data.status === purchaseStatus.DRAFT) {
    timeline.push({
      name: purchaseStatus.DRAFT,
      time: tsNow,
      by: data.requestedBy,
    });

    return {
      status: purchaseStatus.DRAFT,
      statusTimeline: timeline,
    };
  }

  // Case 3: COMPLETED (auto-flow full cycle)
  timeline.push(
    {
      name: purchaseStatus.DRAFT,
      time: tsNow,
      by: data.requestedBy,
    },
    {
      name: purchaseStatus.SUBMITTED,
      time: tsNow,
      by: data.requestedBy,
    },
    {
      name: purchaseStatus.APPROVED,
      time: tsNow,
      by: data.requestedBy,
    },
    {
      name: purchaseStatus.COMPLETED,
      time: tsNow,
      by: data.requestedBy,
    }
  );

  return {
    status: purchaseStatus.COMPLETED,
    statusTimeline: timeline,
  };
};

const createPR = async (payload) => {
  try {
    const data = handleValidation(payload, prSchema);
    if (!data) return;

    const prNumber = await getNextPurchaseRequestId(data.tenantId);
    const isApprovalNeed = await isApprovalNeeded(data.tenantId, "prApproval");

    const items = data.items.map((item) => {
      const result = {
        ordinal: item.ordinal,
        itemName: item.itemName,
        itemId: item.itemId,
        itemCode: item.itemCode,
        categoryId: item.categoryId,
        subcategoryId: item.subcategoryId,
        categoryName: item.categoryName,
        subcategoryName: item.subcategoryName,
        pkg: item.pkg,
        quantity: item.quantity,
        pkgUOM: item.pkgUOM || null,
        purchaseUOM: item.purchaseUOM,
        vendor: item.vendor,
        remarks: item.remarks || null,
        hsnCode: item.hsnCode,
        contractType: item.contractType,
        contractPrice: item.contractPrice,
        contractId: item.contractId || null,
        inStock: item.inStock,
        inclTax: item.inclTax,
        taxRate: item.taxRate,
        stockable: item.stockable,
        unitCost: rupeeToPaise(item.unitCost),
        ...formatAmountToPaise(item),
      };
      return result;
    });

    let requestData = {
      prNumber,
      deliveryDate: FD.toFirestore(data.deliveryDate, TIME_OPTION.START),
      location: data.location,
      inventoryLocation: data.inventoryLocation,
      requestedBy: data.requestedBy,
      lastUpdatedTime: FD.now(),
      tenantId: data.tenantId,
      vendorType: data.vendorType,
      items,
      statusTimeline: [],
      ...formatAmountToPaise(data),
    };

    if (data.transferNumber) {
      requestData.transferNumber = data.transferNumber;
      requestData.transferWorkAreaId = data.transferWorkAreaId;
      requestData.transferId = data.transferId;
    }

    const { status, statusTimeline } = buildStatusTimeline(
      data,
      isApprovalNeed
    );

    requestData.status = status;
    requestData.statusTimeline = statusTimeline;

    let result = await savePurchaseRequest(requestData);

    // format data to return
    result.items = items.map((item) => ({
      ...item,
      ...formatAmountToRupee(item),
      unitCost: paiseToRupee(item.unitCost),
    }));
    result.deliveryDate = new Date(
      FD.toFormattedDate(result.deliveryDate, DATE_FORMAT.DATE_ONLY)
    );

    result = { ...result, ...formatAmountToRupee(result) };

    return result;
  } catch (err) {
    throw Error(err.message);
  }
};

const getPRById = async (id, format = true) => {
  try {
    const data = await getPurchaseRequestById(id);
    if (!data) return null;

    const items = data.items.map((item) => ({
      ordinal: item.ordinal,
      itemName: item.itemName,
      itemId: item.itemId,
      itemCode: item.itemCode,
      categoryId: item.categoryId,
      subcategoryId: item.subcategoryId,
      categoryName: item.categoryName,
      subcategoryName: item.subcategoryName,
      pkg: item.pkg,
      quantity: item.quantity,
      pkgUOM: item.pkgUOM,
      purchaseUOM: item.purchaseUOM,
      vendor: item.vendor,
      remarks: item.remarks,
      hsnCode: item.hsnCode,
      contractType: item.contractType,
      contractPrice: item.contractPrice,
      contractId: item.contractId,
      contractNumber: item.contractNumber || null,
      inStock: item.inStock,
      inclTax: item.inclTax,
      taxRate: item.taxRate,
      stockable: item.stockable,
      unitCost: format ? paiseToRupee(item.unitCost) : item.unitCost,
      ...formatAmountToRupee(item, format),
    }));

    return {
      id: data.id,
      prNumber: data.prNumber,
      deliveryDate: format
        ? FD.toFormattedDate(data.deliveryDate, DATE_FORMAT.DATE_ONLY)
        : data.deliveryDate,
      location: data.location,
      inventoryLocation: data.inventoryLocation,
      requestedBy: data.requestedBy,
      status: data.status,
      lastUpdatedTime: format
        ? FD.toFormattedDate(data.lastUpdatedTime)
        : data.lastUpdatedTime,
      tenantId: data.tenantId,
      vendorType: data.vendorType,
      items,
      statusTimeline: format
        ? data.statusTimeline.map((timeline) => ({
            ...timeline,
            time: FD.toFormattedDate(timeline.time),
          }))
        : data.statusTimeline,
      remarks: data.remarks || null,
      transferNumber: data.transferNumber || null,
      transferWorkAreaId: data.transferWorkAreaId || null,
      transferId: data.transferId || null,
      closedReason: data.closedReason || null,
      rejectedReason: data.rejectedReason || null,
      ...formatAmountToRupee(data, format),
    };
  } catch (err) {
    throw Error(err.message);
  }
};

const updatePR = async (id, payload) => {
  try {
    const data = handleValidation(payload, prSchema);
    if (!data) return;

    if (!data.prNumber)
      data.prNumber = await getNextPurchaseRequestId(data.tenantId);

    const isApprovalNeed = await isApprovalNeeded(data.tenantId, "prApproval");

    const tsNow = FD.now();

    const items = data.items.map((item) => {
      const result = {
        ...item,
        unitCost: rupeeToPaise(item.unitCost),
        vendor: item.vendor,
        ...formatAmountToPaise(item),
      };
      return result;
    });

    const updatedData = {
      ...data,
      items,
      lastUpdatedTime: tsNow,
      deliveryDate: FD.toFirestore(data.deliveryDate, TIME_OPTION.START),
      statusTimeline: data.statusTimeline.map((timeline) => ({
        ...timeline,
        time: FD.toFirestore(timeline.time),
      })),
      ...formatAmountToPaise(data),
    };

    if (data.status === purchaseStatus.SUBMITTED) {
      updatedData.status = purchaseStatus.SUBMITTED;
      updatedData.statusTimeline.push({
        name: purchaseStatus.SUBMITTED,
        time: tsNow,
        by: data.updatedBy,
      });

      if (!isApprovalNeed) {
        updatedData.status = purchaseStatus.APPROVED;
        updatedData.statusTimeline.push({
          name: purchaseStatus.APPROVED,
          time: tsNow,
          by: data.updatedBy,
        });
      }
    }

    if (data.status === purchaseStatus.COMPLETED) {
      updatedData.status = purchaseStatus.COMPLETED;
      updatedData.statusTimeline.push({
        name: purchaseStatus.COMPLETED,
        time: tsNow,
        by: data.updatedBy,
      });
    }

    await updatePurchaseRequestById(id, updatedData);
  } catch (err) {
    throw new Error(`Service Error (updatePurchaseRequest): ${err.message}`);
  }
};

const completePR = async (id, payload) => {
  try {
    payload.status = purchaseStatus.COMPLETED;
    payload.statusTimeline.push({
      name: purchaseStatus.COMPLETED,
      time: FD.now(),
      by: payload.updatedBy,
    });
    await updatePurchaseRequestById(id, payload);
  } catch (err) {
    throw Error(err.message);
  }
};

const approvePR = async (id, approvedBy) => {
  try {
    const data = await getPurchaseRequestById(id);
    if (!data) return null;

    const tsNow = FD.now();

    const updatedTimeline = [
      ...(data.statusTimeline || []),
      {
        name: purchaseStatus.APPROVED,
        time: tsNow,
        by: approvedBy,
      },
    ];

    await updatePurchaseRequestById(id, {
      status: purchaseStatus.APPROVED,
      statusTimeline: updatedTimeline,
      lastUpdatedTime: tsNow,
    });

    return true;
  } catch (err) {
    throw Error(err.message);
  }
};

const rejectPR = async (id, rejectedBy, rejectedReason) => {
  try {
    const data = await getPurchaseRequestById(id);
    if (!data) return null;

    const tsNow = FD.now();

    const updatedTimeline = [
      ...(data.statusTimeline || []),
      {
        name: purchaseStatus.REJECTED,
        time: tsNow,
        by: rejectedBy,
      },
    ];

    await updatePurchaseRequestById(id, {
      status: purchaseStatus.REJECTED,
      statusTimeline: updatedTimeline,
      lastUpdatedTime: tsNow,
      rejectedReason,
      activeStatus: false,
    });

    return true;
  } catch (err) {
    throw Error(err.message);
  }
};

const closePR = async (id, closedBy, reason) => {
  try {
    const data = await getPurchaseRequestById(id);
    if (!data) return null;

    const tsNow = FD.now();

    const updatedTimeline = [
      ...(data.statusTimeline || []),
      {
        name: purchaseStatus.CLOSED,
        time: tsNow,
        by: closedBy,
      },
    ];

    await updatePurchaseRequestById(id, {
      status: purchaseStatus.CLOSED,
      statusTimeline: updatedTimeline,
      lastUpdatedTime: tsNow,
      closedReason: reason,
      activeStatus: false,
    });

    return true;
  } catch (err) {
    throw Error(err.message);
  }
};

const convertPO = async (payload) => {
  try {
    // Group items by vendor
    const vendorMap = {};
    for (const item of payload.items) {
      const vendorId = item.vendor.id;
      if (!vendorMap[vendorId]) {
        vendorMap[vendorId] = {
          items: [],
        };
      }

      const { vendor, ...rest } = item;
      vendorMap[vendorId].items.push(rest);
    }

    const tsNow = FD.now();

    const purchaseOrders = [];
    for (const vendorId in vendorMap) {
      const { items } = vendorMap[vendorId];
      const [poNumber, vendor] = await Promise.all([
        getNextPurchaseOrderId(payload.tenantId),
        getVendorById(payload.tenantId, vendorId),
      ]);

      const requestData = {
        poNumber,
        vendor,
        location: payload.location,
        inventoryLocation: payload.inventoryLocation,
        items,
        status: purchaseStatus.DRAFT,
        statusTimeline: [
          {
            name: purchaseStatus.DRAFT,
            time: tsNow,
            by: payload.requestedBy,
          },
        ],
        prNumber: payload.prNumber,
        requestedBy: payload.requestedBy,
        tenantId: payload.tenantId,
        requestedTime: tsNow,
        lastUpdatedTime: tsNow,
        deliveryDate: payload.deliveryDate,
        grossAmount: payload.grossAmount,
        totalDiscount: payload.totalDiscount,
        netAmount: payload.netAmount,
        totalChargeAmount: payload.totalChargeAmount,
        totalTaxAmount: payload.totalTaxAmount,
        totalAmount: payload.totalAmount,
        totalFocAmount: payload.totalFocAmount,
        totalCess: payload.totalCess,
        charges: payload.charges,
        taxes: payload.taxes,
        roundoff: payload.roundoff,
        remarks: payload.remarks || null,
        transferNumber: payload.transferNumber || null,
        transferWorkAreaId: payload.transferWorkAreaId || null,
        transferId: payload.transferId || null,
      };

      const cart = { ...cartHelper.NewCart("pr"), ...requestData };

      const { type, ...cartResult } = cartHelper.Calculate(cart);

      const validatedData = handleValidation(
        { ...requestData, ...cartResult },
        poSchema
      );
      if (!validatedData) return;

      purchaseOrders.push(validatedData);
    }

    return await savePurchaseOrder(purchaseOrders);
  } catch (err) {
    throw Error(err.message);
  }
};

const createPO = async (payload) => {
  try {
    const isApprovalNeed = await isApprovalNeeded(
      payload.tenantId,
      "poApproval"
    );

    // Group items by vendor
    const vendorMap = {};
    for (const item of payload.items) {
      const vendorId = item.vendor.id;
      if (!vendorMap[vendorId]) {
        vendorMap[vendorId] = {
          items: [],
        };
      }

      const { vendor, ...rest } = item;
      vendorMap[vendorId].items.push(rest);
    }
    const tsNow = FD.now();
    const purchaseOrders = [];

    for (const vendorId in vendorMap) {
      const { items } = vendorMap[vendorId];
      const [poNumber, vendor] = await Promise.all([
        getNextPurchaseOrderId(payload.tenantId),
        getVendorById(payload.tenantId, vendorId),
      ]);

      const requestData = {
        poNumber,
        vendor: {
          ...vendor,
          paymentTerms: payload.paymentTerms || vendor.paymentTerms,
          poTerms: payload.poTerms || vendor.poTerms,
        },
        location: payload.location,
        inventoryLocation: payload.inventoryLocation,
        items: items.map((item) => ({
          ...item,
          unitCost: rupeeToPaise(item.unitCost),
          ...formatAmountToPaise(item),
        })),
        status: purchaseStatus.DRAFT,
        statusTimeline: [
          {
            name: purchaseStatus.DRAFT,
            time: tsNow,
            by: payload.requestedBy,
          },
        ],
        prNumber: payload.prNumber,
        requestedBy: payload.requestedBy,
        tenantId: payload.tenantId,
        requestedTime: tsNow,
        lastUpdatedTime: tsNow,
        deliveryDate: FD.toFirestore(payload.deliveryDate, TIME_OPTION.START),
        remarks: payload.remarks,
        ...formatAmountToPaise(payload),
      };

      if (payload.transferNumber) {
        requestData.transferNumber = payload.transferNumber;
        requestData.transferWorkAreaId = payload.transferWorkAreaId || null;
        requestData.transferId = payload.transferId;
      }

      if (payload.action == purchaseStatus.SUBMITTED) {
        requestData.status = purchaseStatus.SUBMITTED;
        requestData.statusTimeline.push({
          name: purchaseStatus.SUBMITTED,
          time: tsNow,
          by: payload.requestedBy,
        });
      }

      if (payload.action == purchaseStatus.SUBMITTED && !isApprovalNeed) {
        requestData.status = purchaseStatus.APPROVED;
        requestData.statusTimeline.push({
          name: purchaseStatus.APPROVED,
          time: tsNow,
          by: payload.requestedBy,
        });
      }

      const validatedData = handleValidation(requestData, poSchema);
      if (!validatedData) return;

      purchaseOrders.push(validatedData);
    }

    return await savePurchaseOrder(purchaseOrders);
  } catch (err) {
    console.log(err, "errrrrrr");
    throw Error(err.message);
  }
};

const approvePO = async (id, approvedBy) => {
  try {
    const data = await getPOById(id);
    if (!data) return null;

    const updatedTimeline = [
      ...(data.statusTimeline || []),
      {
        name: purchaseStatus.APPROVED,
        time: FD.now(),
        by: approvedBy,
      },
    ];

    await updatePO(id, {
      items: data.items,
      status: purchaseStatus.APPROVED,
      statusTimeline: updatedTimeline,
    });

    return true;
  } catch (err) {
    throw Error(err.message);
  }
};

const rejectPO = async (id, rejectedBy, rejectedReason) => {
  try {
    const data = await getPOById(id);
    if (!data) return null;

    const updatedTimeline = [
      ...(data.statusTimeline || []),
      {
        name: purchaseStatus.REJECTED,
        time: FD.now(),
        by: rejectedBy,
      },
    ];

    await updatePO(id, {
      items: data.items,
      status: purchaseStatus.REJECTED,
      statusTimeline: updatedTimeline,
      rejectedReason,
      activeStatus: false,
    });

    return true;
  } catch (err) {
    throw Error(err.message);
  }
};

const closePO = async (id, closedBy, reason) => {
  try {
    const data = await getPurchaseOrderById(id, false);
    if (!data) return null;

    const tsNow = FD.now();

    const updatedTimeline = [
      ...(data.statusTimeline || []),
      {
        name: purchaseStatus.CLOSED,
        time: tsNow,
        by: closedBy,
      },
    ];

    await updatePurchaseOrderById(id, {
      status: purchaseStatus.CLOSED,
      statusTimeline: updatedTimeline,
      lastUpdatedTime: tsNow,
      closedReason: reason,
      activeStatus: false,
    });

    return true;
  } catch (err) {
    throw Error(err.message);
  }
};

const getPurchaseOrderById = async (id, format = true) => {
  try {
    const data = await getPOById(id);
    if (!data) return null;

    const items = data.items.map((item) => ({
      ordinal: item.ordinal,
      itemName: item.itemName,
      itemId: item.itemId,
      itemCode: item.itemCode,
      categoryId: item.categoryId,
      subcategoryId: item.subcategoryId,
      categoryName: item.categoryName,
      subcategoryName: item.subcategoryName,
      pkg: item.pkg,
      quantity: item.quantity,
      receivedQty: item.receivedQty,
      pkgUOM: item.pkgUOM,
      purchaseUOM: item.purchaseUOM,
      remarks: item.remarks,
      hsnCode: item.hsnCode,
      contractType: item.contractType,
      contractPrice: item.contractPrice,
      contractId: item.contractId,
      contractNumber: item.contractNumber,
      inStock: item.inStock,
      inclTax: item.inclTax,
      taxRate: item.taxRate,
      stockable: item.stockable,
      unitCost: format ? paiseToRupee(item.unitCost) : item.unitCost,
      ...formatAmountToRupee(item),
    }));

    return {
      id: data.id,
      prNumber: data.prNumber,
      poNumber: data.poNumber,
      deliveryDate: format
        ? FD.toFormattedDate(data.deliveryDate, DATE_FORMAT.DATE_ONLY)
        : data.deliveryDate,
      location: data.location,
      inventoryLocation: data.inventoryLocation,
      requestedBy: data.requestedBy,
      status: data.status,
      lastUpdatedTime: format
        ? FD.toFormattedDate(data.lastUpdatedTime)
        : data.lastUpdatedTime,
      tenantId: data.tenantId,
      vendorType: data.vendorType,
      items,
      statusTimeline: format
        ? data.statusTimeline.map((timeline) => ({
            ...timeline,
            time: FD.toFormattedDate(timeline.time),
          }))
        : data.statusTimeline,
      vendor: data.vendor,
      remarks: data.remarks,
      requestedTime: format
        ? FD.toFormattedDate(data.requestedTime)
        : data.requestedTime,
      invoiceDate: format
        ? FD.toFormattedDate(data.invoiceDate, DATE_FORMAT.DATE_ONLY)
        : data.invoiceDate,
      invoiceNumber: data.invoiceNumber,
      grnDate: format
        ? FD.toFormattedDate(data.grnDate, DATE_FORMAT.DATE_ONLY)
        : data.grnDate,
      transferNumber: data.transferNumber || null,
      transferWorkAreaId: data.transferWorkAreaId || null,
      transferId: data.transferId || null,
      closedReason: data.closedReason || null,
      rejectedReason: data.rejectedReason || null,
      ...formatAmountToRupee(data),
    };
  } catch (err) {
    throw Error(err.message);
  }
};

const updatePurchaseOrder = async (id, payload) => {
  try {
    const data = handleValidation(payload, poSchema);
    if (!data) return;

    const isApprovalNeed = await isApprovalNeeded(
      payload.tenantId,
      "poApproval"
    );

    const tsNow = FD.now();

    const items = payload.items.map((item) => {
      return {
        ...item,
        unitCost: rupeeToPaise(item.unitCost),
        ...formatAmountToPaise(item),
      };
    });

    const updatedData = {
      ...payload,
      items,
      deliveryDate: FD.toFirestore(payload.deliveryDate),
      statusTimeline: payload.statusTimeline.map((timeline) => ({
        ...timeline,
        time: FD.toFirestore(timeline.time),
      })),
      ...formatAmountToPaise(payload),
    };

    if (payload.status === purchaseStatus.SUBMITTED) {
      updatedData.status = purchaseStatus.SUBMITTED;
      updatedData.statusTimeline.push({
        name: purchaseStatus.SUBMITTED,
        time: tsNow,
        by: payload.updatedBy,
      });

      if (!isApprovalNeed) {
        updatedData.status = purchaseStatus.APPROVED;
        updatedData.statusTimeline.push({
          name: purchaseStatus.APPROVED,
          time: tsNow,
          by: payload.updatedBy,
        });
      }
    }

    if (payload.status === purchaseStatus.COMPLETED) {
      updatedData.status = purchaseStatus.COMPLETED;
      updatedData.statusTimeline.push({
        name: purchaseStatus.COMPLETED,
        time: tsNow,
        by: payload.updatedBy,
      });
    }

    await updatePO(id, updatedData);
  } catch (err) {
    throw Error(err.message);
  }
};

module.exports = {
  receivePO,
  aggregatePurchaseRequests,
  aggregatePurchaseOrders,
  createPR,
  getPRById,
  updatePR,
  approvePR,
  rejectPR,
  createPO,
  approvePO,
  rejectPO,
  getPurchaseOrderById,
  convertPO,
  completePR,
  updatePurchaseOrder,
  closePR,
  closePO,
};
