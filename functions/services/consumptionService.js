const admin = require("firebase-admin");
const db = admin.firestore();

const {
    prepareMenuRecipeSplitup,
    validateIngredientStock,
} = require("@/services/menuRecipeService");
const schema = require("@/models/consumptionTrackingSchema");
const { handleValidation } = require("@/utils/validation");
const {
    saveConsumptionTracking,
    getAllConsumptionTrackings,
    getByDateRange
} = require("@/repositories/consumptionTrackingRepo");

const { getMenuItemById,getRecordsByFloorId } = require("@/repositories/receipeRepo");

const createConsumptionData = async (tenantId, data) => {
  try {
    const saleDetails = data.saleDetails;
    const floorId = saleDetails.floor_no;
    
    // Fetch all active work areas for the given floor
    const availableWorkAreas = await getRecordsByFloorId(tenantId, floorId);
    
    // Process each item in the sale
    for (const item of saleDetails.items) {
        const itemCode = item.plu_code;
        
        // Fetch recipe details for the menu item
        const requiredRecipe = await getMenuItemById(tenantId, itemCode);
        
        // Initialize consumption data object
        const obj = {
            saleReferenceId: saleDetails.sale_reference_id,
            accountId: saleDetails.account_id,
            storeId: saleDetails.store_id,
            ticketNo: saleDetails.ticket_no,
            tableNo: saleDetails.table_no,
            floorNo: saleDetails.floor_no,
            posMenuItemName: item.menu_item_name,
            posItemCode: item.plu_code,
            servingSizeId: item.serving_size_id,
            servingSizeName: item.serving_size_name,
            soldQuantity: item.quantity,
            posItemTotalAmount: item.total_amount,
        };
        
        // Check if recipe exists and is linked
        if (requiredRecipe && requiredRecipe.linkingStatus === true) {
            
            // Find the work area matching the recipe's tag
            const requiredWorkArea = availableWorkAreas.find(
                wa => wa.tagId === requiredRecipe.tags.id
            );
            
            if (requiredWorkArea) {
                // Assign work area and location details
                obj.workArea = requiredWorkArea.name;
                obj.workAreaId = requiredWorkArea.id;
                obj.location = requiredWorkArea.locationName;
                obj.locationId = requiredWorkArea.locationId;
                obj.invItemName = requiredRecipe.items.name;
                obj.invItemCode = requiredRecipe.items.code;
                obj.invItems = [];
                
                // Find the serving size quantity for this item
                const reductionQty = requiredRecipe.servingLevels.find(
                    level => level.servingSizeName === item.serving_size_name
                );
                
                if (!reductionQty) {
                    // Handle missing serving size
                    obj.status = "error";
                    obj.statusSummary = "Serving size not found";
                } else {
                    // Process based on item type (bought/made vs recipe)
                    if (requiredRecipe.items.itemType && 
                        ['bought', 'made'].includes(requiredRecipe.items.itemType)) {
                        
                        // inventory item - direct consumption calculation
                        const invItem = {
                            itemName: requiredRecipe.items.name,
                            itemCode: requiredRecipe.items.code,
                            itemType: requiredRecipe.items.itemType,
                            recipeUom: requiredRecipe.items.recipeUnit.symbol,
                            cost: 0,
                            requiredQty: reductionQty.qty * item.quantity
                        };
                        
                        obj.invItems.push(invItem);
                        
                    } else {
                        // recipe item - break down into ingredients
                        const ingredientDetails = await prepareMenuRecipeSplitup(
                            tenantId,
                            requiredRecipe.items.id,
                            requiredWorkArea.id
                        );
                        
                        // Calculate conversion ratio: (serving size qty / base recipe qty)
                        const conversionRatio = reductionQty.qty / ingredientDetails.quantity;
                        
                        // Process each ingredient in the recipe
                        for (const ingredient of ingredientDetails.ingredients) {
                            const invItem = {
                                itemName: ingredient.itemName,
                                itemCode: ingredient.itemCode,
                                itemType: ingredient.itemType,
                                recipeUom: ingredient.unit,
                                cost: 0,
                                // Calculate required qty: conversion ratio × ingredient base qty × sold quantity
                                requiredQty: conversionRatio * ingredient.recipeQty * item.quantity
                            };
                            
                            obj.invItems.push(invItem);
                        }
                    }
                }
                
            } else {
                // Work area not found for this recipe's tag
                obj.workArea = null;
                obj.workAreaId = null;
                obj.location = null;
                obj.locationId = null;
                obj.status = "error";
                obj.statusSummary = "Work area not found";
            }
            
        } else {
            // Recipe not found or not linked to inventory
            obj.workArea = null;
            obj.workAreaId = null;
            obj.location = null;
            obj.locationId = null;
            obj.status = "error";
            obj.statusSummary = "Menu item not found or not linked";
        }
        
        console.log("🚀 ~ createConsumptionData ~ obj:", obj);

        // Add tenantId to consumption object and persist to database
        try {
            obj.tenantId = tenantId;

            // Validate and save to database
            const validatedData = handleValidation(obj, schema);
            if (validatedData) {
                await saveConsumptionTracking(validatedData);
                console.log("✅ Consumption tracking data saved successfully");
            }
        } catch (error) {
            console.error("❌ Error saving consumption tracking data:", error);
        }
    }

    return saleDetails;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Get consumption tracking records with filters
 * @param {Object} filters - Filter criteria including tenantId, dateRange, etc.
 * @returns {Promise<Array>} Array of consumption tracking records
 */
const getConsumptionTrackings = async (filters) => {
  try {
    return await getAllConsumptionTrackings(filters);
  } catch (error) {
    console.error("Error fetching consumption trackings:", error);
    throw new Error(`Failed to fetch consumption trackings: ${error.message}`);
  }
};

/**
 * Generate mock consumption data for testing/demo purposes
 * @param {string} tenantId - Tenant ID
 * @returns {Array} Mock consumption tracking data
 */
const generateMockConsumptionData = (tenantId) => {
  return [
    {
      id: "mock-consumption-1",
      saleReferenceId: "SALE-2024-001",
      accountId: "ACC-001",
      storeId: "STORE-001",
      ticketNo: "TKT-001",
      tableNo: "T-05",
      floorNo: "F-01",
      posMenuItemName: "Chicken Burger",
      posItemCode: "CB-001",
      servingSizeId: "SS-001",
      servingSizeName: "Regular",
      soldQuantity: 2,
      posItemTotalAmount: 450.00,
      workArea: "Main Kitchen",
      workAreaId: "WA-001",
      location: "Ground Floor",
      locationId: "LOC-001",
      invItemName: "Chicken Patty",
      invItemCode: "CP-001",
      invItems: [
        {
          itemName: "Chicken Breast",
          itemCode: "CHK-001",
          itemType: "bought",
          recipeUom: "kg",
          cost: 180.00,
          requiredQty: 0.4
        }
      ],
      status: "success",
      tenantId: tenantId,
      createdAt: new Date().toISOString(),
    },
    {
      id: "mock-consumption-2",
      saleReferenceId: "SALE-2024-002",
      accountId: "ACC-001",
      storeId: "STORE-001",
      ticketNo: "TKT-002",
      tableNo: "T-03",
      floorNo: "F-01",
      posMenuItemName: "Margherita Pizza",
      posItemCode: "MP-001",
      servingSizeId: "SS-002",
      servingSizeName: "Large",
      soldQuantity: 1,
      posItemTotalAmount: 320.00,
      workArea: "Pizza Station",
      workAreaId: "WA-002",
      location: "Ground Floor",
      locationId: "LOC-001",
      invItemName: "Pizza Base",
      invItemCode: "PB-001",
      invItems: [
        {
          itemName: "Pizza Dough",
          itemCode: "PD-001",
          itemType: "made",
          recipeUom: "piece",
          cost: 25.00,
          requiredQty: 1
        },
        {
          itemName: "Mozzarella Cheese",
          itemCode: "MC-001",
          itemType: "bought",
          recipeUom: "gm",
          cost: 120.00,
          requiredQty: 150
        }
      ],
      status: "success",
      tenantId: tenantId,
      createdAt: new Date().toISOString(),
    }
  ];
};

module.exports = {
  createConsumptionData,
  getConsumptionTrackings,
  generateMockConsumptionData,
};
