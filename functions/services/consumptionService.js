const admin = require("firebase-admin");
const db = admin.firestore();

const {
    prepareMenuRecipeSplitup,
    validateIngredientStock,
} = require("@/services/menuRecipeService");
// const schema = require("@/models/consumptionSchema");
// const { handleValidation } = require("@/utils/validation");
// const { CONSUMPTION_COLLECTION } = require("@/defs/collectionDefs");

const { getMenuItemById,getRecordsByFloorId } = require("@/repositories/receipeRepo");

const createConsumptionData = async (tenantId, data) => {
  try {
    const saleDetails = data.saleDetails;
    const floorId = saleDetails.floor_no;
    
    // Fetch all active work areas for the given floor
    const availableWorkAreas = await getRecordsByFloorId(tenantId, floorId);
    
    // Process each item in the sale
    for (const item of saleDetails.items) {
        const itemCode = item.plu_code;
        
        // Fetch recipe details for the menu item
        const requiredRecipe = await getMenuItemById(tenantId, itemCode);
        
        // Initialize consumption data object
        const obj = {
            saleReferenceId: saleDetails.sale_reference_id,
            accountId: saleDetails.account_id,
            storeId: saleDetails.store_id,
            ticketNo: saleDetails.ticket_no,
            tableNo: saleDetails.table_no,
            floorNo: saleDetails.floor_no,
            posMenuItemName: item.menu_item_name,
            posItemCode: item.plu_code,
            servingSizeId: item.serving_size_id,
            servingSizeName: item.serving_size_name,
            soldQuantity: item.quantity,
            posItemTotalAmount: item.total_amount,
        };
        
        // Check if recipe exists and is linked
        if (requiredRecipe && requiredRecipe.linkingStatus === true) {
            
            // Find the work area matching the recipe's tag
            const requiredWorkArea = availableWorkAreas.find(
                wa => wa.tagId === requiredRecipe.tags.id
            );
            
            if (requiredWorkArea) {
                // Assign work area and location details
                obj.workArea = requiredWorkArea.name;
                obj.workAreaId = requiredWorkArea.id;
                obj.location = requiredWorkArea.locationName;
                obj.locationId = requiredWorkArea.locationId;
                obj.invItemName = requiredRecipe.items.name;
                obj.invItemCode = requiredRecipe.items.code;
                obj.invItems = [];
                
                // Find the serving size quantity for this item
                const reductionQty = requiredRecipe.servingLevels.find(
                    level => level.servingSizeName === item.serving_size_name
                );
                
                if (!reductionQty) {
                    // Handle missing serving size
                    obj.status = "error";
                    obj.statusSummary = "Serving size not found";
                } else {
                    // Process based on item type (bought/made vs recipe)
                    if (requiredRecipe.items.itemType && 
                        ['bought', 'made'].includes(requiredRecipe.items.itemType)) {
                        
                        // inventory item - direct consumption calculation
                        const invItem = {
                            itemName: requiredRecipe.items.name,
                            itemCode: requiredRecipe.items.code,
                            itemType: requiredRecipe.items.itemType,
                            recipeUom: requiredRecipe.items.recipeUnit.symbol,
                            cost: 0,
                            requiredQty: reductionQty.qty * item.quantity
                        };
                        
                        obj.invItems.push(invItem);
                        
                    } else {
                        // recipe item - break down into ingredients
                        const ingredientDetails = await prepareMenuRecipeSplitup(
                            tenantId,
                            requiredRecipe.items.id,
                            requiredWorkArea.id
                        );
                        
                        // Calculate conversion ratio: (serving size qty / base recipe qty)
                        const conversionRatio = reductionQty.qty / ingredientDetails.quantity;
                        
                        // Process each ingredient in the recipe
                        for (const ingredient of ingredientDetails.ingredients) {
                            const invItem = {
                                itemName: ingredient.itemName,
                                itemCode: ingredient.itemCode,
                                itemType: ingredient.itemType,
                                recipeUom: ingredient.unit,
                                cost: 0,
                                // Calculate required qty: conversion ratio × ingredient base qty × sold quantity
                                requiredQty: conversionRatio * ingredient.recipeQty * item.quantity
                            };
                            
                            obj.invItems.push(invItem);
                        }
                    }
                }
                
            } else {
                // Work area not found for this recipe's tag
                obj.workArea = null;
                obj.workAreaId = null;
                obj.location = null;
                obj.locationId = null;
                obj.status = "error";
                obj.statusSummary = "Work area not found";
            }
            
        } else {
            // Recipe not found or not linked to inventory
            obj.workArea = null;
            obj.workAreaId = null;
            obj.location = null;
            obj.locationId = null;
            obj.status = "error";
            obj.statusSummary = "Menu item not found or not linked";
        }
        
        console.log("🚀 ~ createConsumptionData ~ obj:", obj);
    }




    return saleDetails;
  } catch (error) {
    throw new Error(error.message);
  }
};

module.exports = {
  createConsumptionData,
};
