const { REPORTS, REPORT_INFORMATION } = require("@/defs/reportDefs");
const {
  validateAndPrepareFilters,
  constructColumns,
  validateIdentityFilters,
} = require("@/helpers/reportHelper");
const { ResultType } = require("@/helpers/render");
const { createXlsxReport } = require("@/helpers/xlsxReportUtility");
const {
  DATE_FORMAT,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");

const {
  fetchTransferReportData,
  fetchTenantsReportData,
  fetchReportGroupsData,
} = require("@/repositories/stockMovementReportRepo");
const { paiseToRupee, truncateNumber } = require("@/utils/money");

/**
 * NewProcurementReport
 * --------------------
 * Identifies collection, determines aggregateType,
 * fetches snapshot immediately on construction,
 * and exposes result scaffolding + context.
 */
class NewProcurementReport {
  #headers = [];
  constructor(tenantId, payload, reportType, identity) {
    if (!tenantId) throw new Error("Tenant ID is required");

    const reportInfo = REPORT_INFORMATION[reportType];
    if (!reportInfo) throw new Error("Invalid report type");

    this.#headers = reportInfo.headers;

    const { filters = {}, columns = [] } = payload || {};

    let _filters = validateAndPrepareFilters(tenantId, filters);
    // ✅ identity-aware filtering
    if (identity) {
      _filters = validateIdentityFilters(identity, _filters);
    }

    // Expose everything upfront
    this._options = {
      _filters,
      columns,
      reportType,
      aggregateType: reportInfo.aggregateType,
    };

    // Create default result skeleton
    this.result = {
      id: reportInfo.id,
      name: reportInfo.name,
      headers: this.#headers,
      tenantId,
      data: [],
      totalRow: {},
      payload,
      _meta: null,
    };

    // fetch immediately (sync-like)
    // store the promise — controller will await it
    const { fetchFn } = this.#identify(this._options._filters, reportInfo.id);
    this._snapPromise = fetchFn(tenantId, this._options._filters);
  }

  /**
   * Resolve Firestore snapshot
   */
  async snap() {
    this._snap = await this._snapPromise;
    return this._snap;
  }

  generate(resultType) {
    this.result.headers = constructColumns(
      this.#headers,
      this._options.columns,
      this.result._meta
    );
    return this.#output(resultType);
  }

  #output(resultType) {
    switch (resultType) {
      case ResultType.EXCEL:
        return createXlsxReport(this.result);
      default:
        return this.result;
    }
  }

  /**
   * Identify collection and fetch function.
   * @private
   */
  #identify(filters, reportType) {
    switch (reportType) {
      case "item-wise-stock-movements":
      case "physical-closing":
      case "system-closing":
        return {
          fetchFn: (tenantId, filters) =>
            fetchTenantsReportData(tenantId, filters),
        };

      case "store-variance":
        return {
          fetchFn: (tenantId, filters) =>
            fetchTenantsReportData(tenantId, filters, true),
        };

      case "inventory-consumption":
        return {
          fetchFn: (tenantId, filters) =>
            fetchTenantsReportData(tenantId, filters, false),
        };

      case "bar-variance":
        return {
          fetchFn: (tenantId, filters) =>
            fetchTenantsReportData(tenantId, filters, false, true),
        };

      case "cost-of-issue-vs-revenue":
        return {
          fetchFn: fetchReportGroupsData,
        };

      default:
        return {
          fetchFn: fetchTransferReportData,
        };
    }
  }
}

/**
 * Transfer List Report
 * ---------------------
 */
const getTransferListReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.TRANSFER_LIST,
    identity
  );
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };

  snapshot.forEach((doc) => {
    const data = doc.data();

    const result = {
      transferNo: data.transferNumber,
      fromLocation: data.issuer?.locationName,
      fromWorkArea: data.issuer?.name,
      toLocation: data.requester?.locationName,
      toWorkArea: data.requester?.name,
      createdAt: FD.toFormattedDate(data.requestedBy?.time),
      createdDate: FD.toFormattedDate(
        data.requestedBy?.time,
        DATE_FORMAT.DATE_ONLY
      ),
      createdTime: FD.toFormattedDate(
        data.requestedBy?.time,
        DATE_FORMAT.TIME_ONLY
      ),
      createdBy: data.requestedBy?.name,
      dispatchStatus: data.dispatchStatus,
      receiveStatus: data.receiveStatus,
    };

    report.result.data.push(result);
    report.result.totalRow.totalValue += data.totalValue;
  });

  return report.generate(resultType);
};

/**
 * Dispatch Transfer Report
 * ---------------------
 */
const getDispatchTransferReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.DISPATCH_TRANSFER,
    identity
  );
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };

  snapshot.forEach((doc) => {
    const data = doc.data();

    data.timeLine.forEach((timeline) => {
      const result = {
        transferNo: data.transferNumber,
        dispatchNo: timeline.dispatchNo,
        dispatchedBy: timeline.dispatchedBy?.name,
        dispatchedDate: FD.toFormattedDate(
          timeline.dispatchedBy?.time,
          DATE_FORMAT.DATE_ONLY
        ),
        dispatchedTime: FD.toFormattedDate(
          timeline.dispatchedBy?.time,
          DATE_FORMAT.TIME_ONLY
        ),
        // dispatchedAt: FD.toFormattedDate(timeline.dispatchedBy?.time),
        status: timeline.status,
      };
      report.result.data.push(result);
    });
    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};

/**
 * Detailed Transfer Report
 * ---------------------
 */
const getDetailedTransferReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.DETAILED_TRANSFER,
    identity
  );
  const snapshot = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    totalDispatchCost: 0,
    totalReceiveCost: 0,
  };

  snapshot.forEach((doc) => {
    const data = doc.data();
    data.items.forEach((item) => {
      const totalDispatchCost = paiseToRupee(item.totalValueDispatch) || 0;
      // const totalReceiveCost = paiseToRupee(item.totalValueReceive) || 0;
      const cost =
        item?.dispatchedQuantity > 0
          ? truncateNumber(totalDispatchCost / item.dispatchedQuantity)
          : item?.unitCost || 0;

      const totalReceiveCost = truncateNumber(cost * item.receivedQuantity);

      const reasons = (data.timeLine || []).flatMap((s) =>
        (s.items || [])
          .filter((i) => i.itemId === item.itemId && i.reason)
          .map((i) => i.reason.trim())
      );

      const shortageReason = [...new Set(reasons)].join(", ") || "-";

      const result = {
        transferNo: data.transferNumber,
        fromLocation: data.issuer?.locationName,
        fromWorkArea: data.issuer?.name,
        toLocation: data.requester?.locationName,
        toWorkArea: data.requester?.name,
        createdAt: FD.toFormattedDate(data.requestedBy?.time),
        createdDate: FD.toFormattedDate(
          data.requestedBy?.time,
          DATE_FORMAT.DATE_ONLY
        ),
        createdTime: FD.toFormattedDate(
          data.requestedBy?.time,
          DATE_FORMAT.TIME_ONLY
        ),
        createdBy: data.requestedBy?.name,
        itemName: item.itemName,
        itemCode: item.itemCode,
        categoryName: item.categoryName,
        subcategoryName: item.subcategoryName,
        unitCost: cost,
        pkg: item?.pkg.id === "default" ? item.purchaseUOM : item?.pkg.name,
        requestedQuantity: item.requestedQuantity,
        dispatchedQuantity: item.dispatchedQuantity || 0,
        receivedQuantity: item.receivedQuantity || 0,
        shortageQuantity: item.shortageQuantity || 0,
        totalDispatchCost,
        totalReceiveCost,
        shortageReason,
      };
      report.result.data.push(result);

      report.result.totalRow.totalDispatchCost += totalDispatchCost;
      report.result.totalRow.totalReceiveCost += totalReceiveCost;

      report.result.totalRow.totalDispatchCost = truncateNumber(
        report.result.totalRow.totalDispatchCost
      );
      report.result.totalRow.totalReceiveCost = truncateNumber(
        report.result.totalRow.totalReceiveCost
      );
    });
    report.result.totalRow.totalValue += data.totalValue;
  });
  return report.generate(resultType);
};

// cost-of-issue-vs-revenue
const getCostOfIssueVsRevenueReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.COST_OF_ISSUE_VS_REVENUE,
    identity
  );

  const groupsData = await report.snap();

  report.result.data = [];
  report.result.expand = true;
  report.result.totalRow = {
    group: "OVERALL",
    sales: 0,
    costOfIssue: 0,
    costingPercentage: 0,
  };

  groupsData.forEach((data) => {
    const groupName = data.name?.toUpperCase();
    const groupSales = data.netSales || 0;
    let groupCost = 0;

    const subItems = data.category.map((cat) => {
      const cost = paiseToRupee(cat.cost) || 0;
      groupCost += cost;

      return {
        group: cat.name,
        sales: "-",
        costOfIssue: cost,
        costingPercentage: groupSales
          ? truncateNumber((cost / groupSales) * 100)
          : 0,
      };
    });

    const groupCostingPercentage = groupSales
      ? truncateNumber((groupCost / groupSales) * 100)
      : 0;

    report.result.totalRow.sales += groupSales;
    report.result.totalRow.costOfIssue += groupCost;
    report.result.totalRow.costingPercentage = report.result.totalRow.sales
      ? truncateNumber(
          (report.result.totalRow.costOfIssue / report.result.totalRow.sales) *
            100
        )
      : 0;

    report.result.data.push({
      id: data.id,
      group: groupName,
      sales: groupSales,
      costOfIssue: groupCost,
      costingPercentage: groupCostingPercentage,
      subItems,
    });
  });

  return report.generate(resultType);
};

// item-wise stock movement
const getItemWiseStockMovementsReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.ITEM_WISE_STOCK_MOVEMENTS,
    identity
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    openingAmount: 0,
    purchaseAmount: 0,
    transferInAmount: 0,
    transferOutAmount: 0,
    spoilageAmount: 0,
    adjustmentAmount: 0,
    consumptionAmount: 0,
    closingAmount: 0,
    varianceAmount: 0,
    expectedAmount: 0,
  };

  allDocs.forEach((data) => {
    const openingAmount = data?.opening?.totalValue
      ? paiseToRupee(data.opening.totalValue)
      : 0;
    const purchaseAmount = data?.purchase?.totalValue
      ? paiseToRupee(data.purchase.totalValue)
      : 0;
    const transferInAmount = data?.transferIn?.totalValue
      ? paiseToRupee(data.transferIn.totalValue)
      : 0;
    const transferOutAmount = data?.transferOut?.totalValue
      ? paiseToRupee(data.transferOut.totalValue)
      : 0;
    const spoilageAmount = data?.spoilage?.totalValue
      ? paiseToRupee(data.spoilage.totalValue)
      : 0;
    const adjustmentAmount = data?.adjustment?.totalValue
      ? paiseToRupee(data.adjustment.totalValue)
      : 0;
    const consumptionAmount = data?.consumption?.totalValue
      ? paiseToRupee(data.consumption.totalValue)
      : 0;
    const closingAmount = data?.physicalClosing?.totalValue
      ? paiseToRupee(data.physicalClosing.totalValue)
      : 0;
    const varianceAmount = data?.closingAdjustment?.totalValue
      ? paiseToRupee(data?.closingAdjustment?.totalValue)
      : 0;
    const expectedAmount = data?.systemClosing?.totalValue
      ? paiseToRupee(data.systemClosing.totalValue)
      : 0;

    const result = {
      itemName: data.inventoryItemName,
      itemCode: data.inventoryItemCode,
      businessDate: data.businessDate,
      location: data.locationName,
      workArea: data.inventoryLocationName,
      hsnCode: data?.hsnCode || "-",
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      pkg: data?.pkgName || "-",
      unitCost: data?.unitCost || 0,
      openingQty: data?.opening?.qty || 0,
      openingAmount,
      purchaseQty: data?.purchase?.qty || 0,
      purchaseAmount,
      transferInQty: data?.transferIn?.qty || 0,
      transferInAmount,
      transferOutQty: data?.transferOut?.qty || 0,
      transferOutAmount,
      spoilageQty: data?.spoilage?.qty || 0,
      spoilageAmount,
      adjustmentQty: data?.adjustment?.qty || 0,
      adjustmentAmount,
      consumptionQty: data?.consumption?.qty || 0,
      consumptionAmount,
      expectedQty: data?.systemClosing?.qty || 0,
      expectedAmount,
      closingQty: data?.physicalClosing?.qty || 0,
      closingAmount,
      varianceQty: data?.closingAdjustment?.qty || 0,
      varianceAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.openingAmount += openingAmount;
    report.result.totalRow.purchaseAmount += purchaseAmount;
    report.result.totalRow.transferInAmount += transferInAmount;
    report.result.totalRow.transferOutAmount += transferOutAmount;
    report.result.totalRow.spoilageAmount += spoilageAmount;
    report.result.totalRow.adjustmentAmount += adjustmentAmount;
    report.result.totalRow.consumptionAmount += consumptionAmount;
    report.result.totalRow.closingAmount += closingAmount;
    report.result.totalRow.varianceAmount += varianceAmount;
    report.result.totalRow.expectedAmount += expectedAmount;

    report.result.totalRow.totalValue += data.totalValue || 0;
  });

  return report.generate(resultType);
};

const getPhysicalClosingReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.PHYSICAL_CLOSING,
    identity
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    totalQuantity: 0,
  };

  allDocs.forEach((data) => {
    const closingAmount = data?.physicalClosing?.totalValue
      ? paiseToRupee(data.physicalClosing.totalValue)
      : 0;

    const result = {
      itemName: data.inventoryItemName,
      itemCode: data.inventoryItemCode,
      businessDate: data.businessDate,
      location: data.locationName,
      workArea: data.inventoryLocationName,
      hsnCode: data?.hsnCode || "-",
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      pkg: data?.pkgName || "-",
      unitCost: data?.unitCost || 0,
      quantity: data?.physicalClosing?.qty || 0,
      rate: truncateNumber(closingAmount / data?.physicalClosing?.qty, 2),
      totalValue: closingAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.totalValue += closingAmount || 0;
    report.result.totalRow.totalQuantity += data?.physicalClosing?.qty || 0;
  });

  return report.generate(resultType);
};

const getSystemClosingReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.SYSTEM_CLOSING,
    identity
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    totalQuantity: 0,
  };

  allDocs.forEach((data) => {
    const closingAmount = data?.systemClosing?.totalValue
      ? paiseToRupee(data.systemClosing.totalValue)
      : 0;

    const result = {
      itemName: data.inventoryItemName,
      itemCode: data.inventoryItemCode,
      businessDate: data.businessDate,
      location: data.locationName,
      workArea: data.inventoryLocationName,
      hsnCode: data?.hsnCode || "-",
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      pkg: data?.pkgName || "-",
      unitCost: data?.unitCost || 0,
      quantity: data?.systemClosing?.qty || 0,
      rate: truncateNumber(closingAmount / data?.systemClosing?.qty, 2),
      totalValue: closingAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.totalValue += closingAmount || 0;
    report.result.totalRow.totalQuantity += data?.systemClosing?.qty || 0;
  });

  return report.generate(resultType);
};

// Short supply Report
// requested vs issued
const getShortSupplyReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.SHORT_SUPPLY,
    identity
  );
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };
  snapshot.forEach((doc) => {
    const data = doc.data();
    data.items.forEach((item) => {
      const result = {
        transferNo: data.transferNumber,
        itemName: item.itemName,
        itemCode: item.itemCode,
        pkg: item?.pkg.id === "default" ? item.purchaseUOM : item?.pkg.name,
        requestedQuantity: item.requestedQuantity,
        dispatchedQuantity: item.dispatchedQuantity || 0,
        shortageQuantity:
          item.requestedQuantity - (item.dispatchedQuantity || 0),
      };
      report.result.data.push(result);
    });
    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};

// store variance
const getStoreVarianceReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.STORE_VARIANCE,
    identity
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    openingAmount: 0,
    purchaseAmount: 0,
    transferInAmount: 0,
    transferOutAmount: 0,
    spoilageAmount: 0,
    adjustmentAmount: 0,
    closingAmount: 0,
    varianceAmount: 0,
    expectedAmount: 0,
  };

  allDocs.forEach((data) => {
    const openingAmount = data?.opening?.totalValue
      ? paiseToRupee(data.opening.totalValue)
      : 0;
    const purchaseAmount = data?.purchase?.totalValue
      ? paiseToRupee(data.purchase.totalValue)
      : 0;
    const transferInAmount = data?.transferIn?.totalValue
      ? paiseToRupee(data.transferIn.totalValue)
      : 0;
    const transferOutAmount = data?.transferOut?.totalValue
      ? paiseToRupee(data.transferOut.totalValue)
      : 0;
    const spoilageAmount = data?.spoilage?.totalValue
      ? paiseToRupee(data.spoilage.totalValue)
      : 0;
    const adjustmentAmount = data?.adjustment?.totalValue
      ? paiseToRupee(data.adjustment.totalValue)
      : 0;
    const closingAmount = data?.physicalClosing?.totalValue
      ? paiseToRupee(data.physicalClosing.totalValue)
      : 0;
    const varianceAmount = data?.closingAdjustment?.totalValue
      ? paiseToRupee(data?.closingAdjustment?.totalValue)
      : 0;
    const expectedAmount = data?.systemClosing?.totalValue
      ? paiseToRupee(data.systemClosing.totalValue)
      : 0;

    const result = {

      location: data.locationName,
      workArea: data.inventoryLocationName,
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      hsnCode: data?.hsnCode || "-",
      itemCode: data.inventoryItemCode,
      itemName: data.inventoryItemName,
      pkg: data?.pkgName || "-",
      // unitCost: data?.unitCost || 0,

      openingQty: data?.opening?.qty || 0,
      purchaseQty: data?.purchase?.qty || 0,
      transferInQty: data?.transferIn?.qty || 0,
      transferOutQty: data?.transferOut?.qty || 0,
      spoilageQty: data?.spoilage?.qty || 0,
      adjustmentQty: data?.adjustment?.qty || 0,
      expectedQty: data?.systemClosing?.qty || 0,
      closingQty: data?.physicalClosing?.qty || 0,
      varianceQty: data?.closingAdjustment?.qty || 0,

      openingAmount,
      purchaseAmount,
      transferInAmount,
      transferOutAmount,
      spoilageAmount,
      adjustmentAmount,
      expectedAmount,
      closingAmount,
      varianceAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.openingAmount += openingAmount;
    report.result.totalRow.purchaseAmount += purchaseAmount;
    report.result.totalRow.transferInAmount += transferInAmount;
    report.result.totalRow.transferOutAmount += transferOutAmount;
    report.result.totalRow.spoilageAmount += spoilageAmount;
    report.result.totalRow.adjustmentAmount += adjustmentAmount;
    report.result.totalRow.closingAmount += closingAmount;
    report.result.totalRow.varianceAmount += varianceAmount;
    report.result.totalRow.expectedAmount += expectedAmount;

    report.result.totalRow.totalValue += data.totalValue || 0;
  });

  return report.generate(resultType);
};

// bar variance
const getBarVarianceReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.BAR_VARIANCE,
    identity
  );  

  const allDocs = await report.snap();  

  report.result.totalRow = {
    totalValue: 0,
    openingAmount: 0,
    transferInAmount: 0,
    transferOutAmount: 0,
    spoilageAmount: 0,
    adjustmentAmount: 0,
    preparationAmount: 0,
    totalStockAmount:0,
    closingAmount: 0,
    actualCost:0,
    consumptionAmount:0,
    varianceAmount: 0
    };

  allDocs.forEach((data) => {
    const openingAmount = data?.opening?.totalValue
      ? paiseToRupee(data.opening.totalValue)
      : 0;
    const transferInAmount = data?.transferIn?.totalValue
      ? paiseToRupee(data.transferIn.totalValue)
      : 0;
    const transferOutAmount = data?.transferOut?.totalValue
      ? paiseToRupee(data.transferOut.totalValue)
      : 0;
    const spoilageAmount = data?.spoilage?.totalValue
      ? paiseToRupee(data.spoilage.totalValue)
      : 0;
    const adjustmentAmount = data?.adjustment?.totalValue
      ? paiseToRupee(data.adjustment.totalValue)
      : 0;
    const consumptionAmount = data?.consumption?.totalValue
      ? paiseToRupee(data.consumption.totalValue)
      : 0;
    const closingAmount = data?.physicalClosing?.totalValue
      ? paiseToRupee(data.physicalClosing.totalValue)
      : 0;

    // @todo
    // 1.Preparation Qty
    // 2.Total Available Stock Qty:
    // inv-item (Opening Qty + Transfer In Qty + Adjustment Qty) - (Transfer Out Qty + Spoilage Qty + consumption Preparation Qty) 
    // made-item (Opening Qty + Transfer In Qty + Adjustment Qty + consumption Preparation Qty) - (Transfer Out Qty + Spoilage Qty) 
 
    const totalStock = 10650;
    const closingQty = data?.physicalClosing?.qty || 0;
    const actualConsumption = totalStock - closingQty;
    const consumptionQty = data?.consumption?.qty || 0;

    const totalStockAmount = 14200;
    const actualCost = totalStockAmount - closingAmount;
    const varianceAmount = actualCost - consumptionAmount;
    const preparationAmount = 0;

    const result = {
      location: data.locationName,
      workArea: data.inventoryLocationName,
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      hsnCode: data?.hsnCode || "-",
      itemCode: data.inventoryItemCode,
      itemName: data.inventoryItemName,
      pkg: data?.pkgName || "-",
      openingQty: data?.opening?.qty || 0,
      transferInQty: data?.transferIn?.qty || 0,
      transferOutQty: data?.transferOut?.qty || 0,
      spoilageQty: data?.spoilage?.qty || 0,
      adjustmentQty: data?.adjustment?.qty || 0,
      preparationQty: 0,
      totalStock,
      closingQty,
      actualConsumption,
      consumptionQty,
      varianceQty: actualConsumption - consumptionQty,

      openingAmount,
      transferInAmount,
      transferOutAmount,
      spoilageAmount,
      adjustmentAmount,
      preparationAmount,
      totalStockAmount,
      closingAmount,
      actualCost,
      consumptionAmount,
      varianceAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.openingAmount += openingAmount;
    report.result.totalRow.transferInAmount += transferInAmount;
    report.result.totalRow.transferOutAmount += transferOutAmount;
    report.result.totalRow.spoilageAmount += spoilageAmount;
    report.result.totalRow.adjustmentAmount += adjustmentAmount;
    report.result.totalRow.preparationAmount += preparationAmount;
    report.result.totalRow.totalStockAmount += totalStockAmount;
    report.result.totalRow.closingAmount += closingAmount;
    report.result.totalRow.actualCost += actualCost;
    report.result.totalRow.consumptionAmount += consumptionAmount;
    report.result.totalRow.varianceAmount += varianceAmount;

    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};

// flr report
const getFLRReport = (tenantId, payload, resultType) => {
  const reportInfo = REPORT_INFORMATION[REPORTS.FLR];
  if (!reportInfo) throw new Error("Invalid report type");

  let result = {
    id: reportInfo.id,
    name: reportInfo.name,
    headers: constructColumns(
      reportInfo.headers,
      payload.columns,
      {},
      "group",
      resultType
    ),
    tenantId,
    data: [
      {
        sno: 1,
        location: "Outlet A",
        categoryName: "LIQUOR",
        subCategoryName: "RUM",
        hsnCode: "-",
        itemCode: "I001",
        itemName: "Bacardi White",
        pkg: "BTL750ml",
        servingSize: "30ml",

        storeClosingPkg: 10,
        storeClosingOpen: 0,
        purchaseQty: 12,
        workAreaClosingPkg: 8,
        workAreaClosingOpen: 150,
        totalQtyPkg: 18,
        totalQtyOpen: 5,
      },
      {
        sno: 2,
        location: "Outlet A",
        categoryName: "LIQUOR",
        subCategoryName: "RUM",
        hsnCode: "-",
        itemCode: "I001",
        itemName: "Bacardi White",
        pkg: "BTL1000ml",
        servingSize: "30ml",

        storeClosingPkg: 5,
        storeClosingOpen: 0,
        purchaseQty: 6,
        workAreaClosingPkg: 4,
        workAreaClosingOpen: 330,
        totalQtyPkg: 9,
        totalQtyOpen:11,
      }
    ],
    totalRow: {

    },
    payload,
    _meta: null
  };

  switch (resultType) {
    case ResultType.EXCEL:
      return createXlsxReport(result)
    default:
      return result
  }
}

// inventory consumption
const getInventoryConsumptionReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.INVENTORY_CONSUMPTION,
    identity
  );  

  const allDocs = await report.snap();  

  report.result.totalRow = {
    totalValue: 0,
    openingAmount: 0,
    transferInAmount: 0,
    transferOutAmount: 0,
    spoilageAmount: 0,
    adjustmentAmount: 0,
    preparationAmount: 0,
    totalStockAmount:0,
    closingAmount: 0,
    actualCost:0,
    consumptionAmount:0,
    varianceAmount: 0
    };

  allDocs.forEach((data) => {
    const openingAmount = data?.opening?.totalValue
      ? paiseToRupee(data.opening.totalValue)
      : 0;
    const transferInAmount = data?.transferIn?.totalValue
      ? paiseToRupee(data.transferIn.totalValue)
      : 0;
    const transferOutAmount = data?.transferOut?.totalValue
      ? paiseToRupee(data.transferOut.totalValue)
      : 0;
    const spoilageAmount = data?.spoilage?.totalValue
      ? paiseToRupee(data.spoilage.totalValue)
      : 0;
    const adjustmentAmount = data?.adjustment?.totalValue
      ? paiseToRupee(data.adjustment.totalValue)
      : 0;
    const consumptionAmount = data?.consumption?.totalValue
      ? paiseToRupee(data.consumption.totalValue)
      : 0;
    const closingAmount = data?.physicalClosing?.totalValue
      ? paiseToRupee(data.physicalClosing.totalValue)
      : 0;

    // @todo
    // 1.Preparation Qty
    // 2.Total Available Stock Qty:
    // inv-item (Opening Qty + Transfer In Qty + Adjustment Qty) - (Transfer Out Qty + Spoilage Qty + consumption Preparation Qty) 
    // made-item (Opening Qty + Transfer In Qty + Adjustment Qty + consumption Preparation Qty) - (Transfer Out Qty + Spoilage Qty) 
 
    const totalStock = 10650;
    const closingQty = data?.physicalClosing?.qty || 0;
    const actualConsumption = totalStock - closingQty;
    const consumptionQty = data?.consumption?.qty || 0;

    const totalStockAmount = 14200;
    const actualCost = totalStockAmount - closingAmount;
    const varianceAmount = actualCost - consumptionAmount;
    const preparationAmount = 0;

    const result = {
      location: data.locationName,
      workArea: data.inventoryLocationName,
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      hsnCode: data?.hsnCode || "-",
      itemCode: data.inventoryItemCode,
      itemName: data.inventoryItemName,
      pkg: data?.pkgName || "-",
      openingQty: data?.opening?.qty || 0,
      transferInQty: data?.transferIn?.qty || 0,
      transferOutQty: data?.transferOut?.qty || 0,
      spoilageQty: data?.spoilage?.qty || 0,
      adjustmentQty: data?.adjustment?.qty || 0,
      preparationQty: 0,
      totalStock,
      closingQty,
      actualConsumption,
      consumptionQty,
      varianceQty: actualConsumption - consumptionQty,

      openingAmount,
      transferInAmount,
      transferOutAmount,
      spoilageAmount,
      adjustmentAmount,
      preparationAmount,
      totalStockAmount,
      closingAmount,
      actualCost,
      consumptionAmount,
      varianceAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.openingAmount += openingAmount;
    report.result.totalRow.transferInAmount += transferInAmount;
    report.result.totalRow.transferOutAmount += transferOutAmount;
    report.result.totalRow.spoilageAmount += spoilageAmount;
    report.result.totalRow.adjustmentAmount += adjustmentAmount;
    report.result.totalRow.preparationAmount += preparationAmount;
    report.result.totalRow.totalStockAmount += totalStockAmount;
    report.result.totalRow.closingAmount += closingAmount;
    report.result.totalRow.actualCost += actualCost;
    report.result.totalRow.consumptionAmount += consumptionAmount;
    report.result.totalRow.varianceAmount += varianceAmount;

    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};

module.exports = {
  getTransferListReport,
  getDispatchTransferReport,
  getDetailedTransferReport,
  getCostOfIssueVsRevenueReport,
  getItemWiseStockMovementsReport,
  getShortSupplyReport,
  getPhysicalClosingReport,
  getSystemClosingReport,
  getStoreVarianceReport,
  getInventoryConsumptionReport,
  getBarVarianceReport,
  getFLRReport
};
