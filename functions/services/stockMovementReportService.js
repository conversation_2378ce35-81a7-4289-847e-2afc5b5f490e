const { REPORTS, REPORT_INFORMATION } = require("@/defs/reportDefs");
const {
  validateAndPrepareFilters,
  constructColumns,
  validateIdentityFilters,
} = require("@/helpers/reportHelper");
const { ResultType } = require("@/helpers/render");
const { createXlsxReport } = require("@/helpers/xlsxReportUtility");
const {
  DATE_FORMAT,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");

const {
  fetchTransferReportData,
  fetchTenantsReportData,
} = require("@/repositories/stockMovementReportRepo");
const { paiseToRupee, truncateNumber } = require("@/utils/money");

/**
 * NewProcurementReport
 * --------------------
 * Identifies collection, determines aggregateType,
 * fetches snapshot immediately on construction,
 * and exposes result scaffolding + context.
 */
class NewProcurementReport {
  #headers = [];
  constructor(tenantId, payload, reportType, identity) {
    if (!tenantId) throw new Error("Tenant ID is required");

    const reportInfo = REPORT_INFORMATION[reportType];
    if (!reportInfo) throw new Error("Invalid report type");

    this.#headers = reportInfo.headers;

    const { filters = {}, columns = [] } = payload || {};

    let _filters = validateAndPrepareFilters(tenantId, filters);
    // ✅ identity-aware filtering
    if (identity) {
      _filters = validateIdentityFilters(identity, _filters);
    }

    // Expose everything upfront
    this._options = {
      _filters,
      columns,
      reportType,
      aggregateType: reportInfo.aggregateType,
    };

    // Create default result skeleton
    this.result = {
      id: reportInfo.id,
      name: reportInfo.name,
      headers: this.#headers,
      tenantId,
      data: [],
      totalRow: {},
      payload,
      _meta: null,
    };

    // fetch immediately (sync-like)
    // store the promise — controller will await it
    const { fetchFn } = this.#identify(this._options._filters, reportInfo.id);
    this._snapPromise = fetchFn(tenantId, this._options._filters);
  }

  /**
   * Resolve Firestore snapshot
   */
  async snap() {
    this._snap = await this._snapPromise;
    return this._snap;
  }

  generate(resultType) {
    this.result.headers = constructColumns(
      this.#headers,
      this._options.columns,
      this.result._meta
    );
    return this.#output(resultType);
  }

  #output(resultType) {
    switch (resultType) {
      case ResultType.EXCEL:
        return createXlsxReport(this.result);
      default:
        return this.result;
    }
  }

  /**
   * Identify collection and fetch function.
   * @private
   */
  #identify(filters, reportType) {
    if (reportType === "item-wise-stock-movements") {
      return { fetchFn: fetchTenantsReportData };
    }
    return { fetchFn: fetchTransferReportData };
  }
}

/**
 * Transfer List Report
 * ---------------------
 */
const getTransferListReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.TRANSFER_LIST,
    identity
  );
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };

  snapshot.forEach((doc) => {
    const data = doc.data();

    const result = {
      transferNo: data.transferNumber,
      fromLocation: data.issuer?.locationName,
      fromWorkArea: data.issuer?.name,
      toLocation: data.requester?.locationName,
      toWorkArea: data.requester?.name,
      createdAt: FD.toFormattedDate(data.requestedBy?.time),
      createdDate: FD.toFormattedDate(
        data.requestedBy?.time,
        DATE_FORMAT.DATE_ONLY
      ),
      createdTime: FD.toFormattedDate(
        data.requestedBy?.time,
        DATE_FORMAT.TIME_ONLY
      ),
      createdBy: data.requestedBy?.name,
      dispatchStatus: data.dispatchStatus,
      receiveStatus: data.receiveStatus,
    };

    report.result.data.push(result);
    report.result.totalRow.totalValue += data.totalValue;
  });

  return report.generate(resultType);
};

/**
 * Dispatch Transfer Report
 * ---------------------
 */
const getDispatchTransferReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.DISPATCH_TRANSFER,
    identity
  );
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };

  snapshot.forEach((doc) => {
    const data = doc.data();

    data.timeLine.forEach((timeline) => {
      const result = {
        transferNo: data.transferNumber,
        dispatchNo: timeline.dispatchNo,
        dispatchedBy: timeline.dispatchedBy?.name,
        dispatchedDate: FD.toFormattedDate(
          timeline.dispatchedBy?.time,
          DATE_FORMAT.DATE_ONLY
        ),
        dispatchedTime: FD.toFormattedDate(
          timeline.dispatchedBy?.time,
          DATE_FORMAT.TIME_ONLY
        ),
        // dispatchedAt: FD.toFormattedDate(timeline.dispatchedBy?.time),
        status: timeline.status,
      };
      report.result.data.push(result);
    });
    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};

/**
 * Detailed Transfer Report
 * ---------------------
 */
const getDetailedTransferReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.DETAILED_TRANSFER,
    identity
  );
  const snapshot = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    totalDispatchCost: 0,
    totalReceiveCost: 0,
  };

  snapshot.forEach((doc) => {
    const data = doc.data();
    data.items.forEach((item) => {
      const totalDispatchCost = paiseToRupee(item.totalValueDispatch) || 0;
      // const totalReceiveCost = paiseToRupee(item.totalValueReceive) || 0;
      const cost =
        item?.dispatchedQuantity > 0
          ? truncateNumber(totalDispatchCost / item.dispatchedQuantity)
          : 0;

      const totalReceiveCost = truncateNumber(cost * item.receivedQuantity);    

      const reasons = (data.timeLine || []).flatMap((s) =>
        (s.items || [])
          .filter((i) => i.itemId === item.itemId && i.reason)
          .map((i) => i.reason.trim())
      );

      const shortageReason = [...new Set(reasons)].join(", ") || "-";

      const result = {
        transferNo: data.transferNumber,
        fromLocation: data.issuer?.locationName,
        fromWorkArea: data.issuer?.name,
        toLocation: data.requester?.locationName,
        toWorkArea: data.requester?.name,
        createdAt: FD.toFormattedDate(data.requestedBy?.time),
        createdDate: FD.toFormattedDate(
          data.requestedBy?.time,
          DATE_FORMAT.DATE_ONLY
        ),
        createdTime: FD.toFormattedDate(
          data.requestedBy?.time,
          DATE_FORMAT.TIME_ONLY
        ),
        createdBy: data.requestedBy?.name,
        itemName: item.itemName,
        itemCode: item.itemCode,
        categoryName: item.categoryName,
        subcategoryName: item.subcategoryName,
        unitCost: cost,
        pkg: item?.pkg.id === "default" ? item.purchaseUOM : item?.pkg.name,
        requestedQuantity: item.requestedQuantity,
        dispatchedQuantity: item.dispatchedQuantity || 0,
        receivedQuantity: item.receivedQuantity || 0,
        shortageQuantity: item.shortageQuantity || 0,
        totalDispatchCost,
        totalReceiveCost,
        shortageReason,
      };
      report.result.data.push(result);

      report.result.totalRow.totalDispatchCost += totalDispatchCost;
      report.result.totalRow.totalReceiveCost += totalReceiveCost;
    });
    report.result.totalRow.totalValue += data.totalValue;
  });
  return report.generate(resultType);
};

// cost-of-issue-vs-revenue
const getCostOfIssueVsRevenueReport = async (tenantId, payload, resultType) => {
  const reportInfo = REPORT_INFORMATION[REPORTS.COST_OF_ISSUE_VS_REVENUE];
  if (!reportInfo) throw new Error("Invalid report type");

  let result = {
    id: reportInfo.id,
    name: reportInfo.name,
    headers: constructColumns(
      reportInfo.headers,
      payload.columns,
      {},
      "group",
      resultType
    ),
    tenantId,
    data: [
      {
        id: "FOOD",
        group: "FOOD",
        sales: 160000,
        costOfIssue: 64000,
        costingPercentage: 40,
        subItems: [
          {
            group: "Cheese",
            sales: "-",
            costOfIssue: 12000,
            costingPercentage: 7.5,
          },
          {
            group: "Dairy",
            sales: "-",
            costOfIssue: 15000,
            costingPercentage: 9.4,
          },
          {
            group: "Vegetables",
            sales: "-",
            costOfIssue: 18000,
            costingPercentage: 11.3,
          },
          {
            group: "Meat",
            sales: "-",
            costOfIssue: 14000,
            costingPercentage: 8.8,
          },
          {
            group: "Bakery",
            sales: "-",
            costOfIssue: 5000,
            costingPercentage: 3.1,
          },
        ],
      },
      {
        id: "BEVERAGE",
        group: "BEVERAGE",
        sales: 90000,
        costOfIssue: 27000,
        costingPercentage: 30,
        subItems: [
          {
            group: "Soft Drinks",
            sales: "-",
            costOfIssue: 7000,
            costingPercentage: 7.8,
          },
          {
            group: "Juices",
            sales: "-",
            costOfIssue: 6000,
            costingPercentage: 6.7,
          },
          {
            group: "Energy Drinks",
            sales: "-",
            costOfIssue: 4000,
            costingPercentage: 4.4,
          },
          {
            group: "Tea & Coffee",
            sales: "-",
            costOfIssue: 6000,
            costingPercentage: 6.7,
          },
          {
            group: "Water",
            sales: "-",
            costOfIssue: 4000,
            costingPercentage: 4.4,
          },
        ],
      },
      {
        id: "LIQUOR",
        group: "LIQUOR",
        sales: 100000,
        costOfIssue: 50000,
        costingPercentage: 50,
        subItems: [
          {
            group: "Whisky",
            sales: "-",
            costOfIssue: 15000,
            costingPercentage: 15,
          },
          {
            group: "Vodka",
            sales: "-",
            costOfIssue: 10000,
            costingPercentage: 10,
          },
          {
            group: "Rum",
            sales: "-",
            costOfIssue: 9000,
            costingPercentage: 9,
          },
          {
            group: "Brandy",
            sales: "-",
            costOfIssue: 8000,
            costingPercentage: 8,
          },
          {
            group: "Beer",
            sales: "-",
            costOfIssue: 8000,
            costingPercentage: 8,
          },
        ],
      },
      {
        id: "TOBACCO",
        group: "TOBACCO",
        sales: 30000,
        costOfIssue: 11000,
        costingPercentage: 36.7,
        subItems: [
          {
            group: "Cigarettes",
            sales: "-",
            costOfIssue: 5000,
            costingPercentage: 16.7,
          },
          {
            group: "Cigars",
            sales: "-",
            costOfIssue: 3000,
            costingPercentage: 10,
          },
          {
            group: "Rolling Tobacco",
            sales: "-",
            costOfIssue: 2000,
            costingPercentage: 6.7,
          },
          {
            group: "Filters",
            sales: "-",
            costOfIssue: 700,
            costingPercentage: 2.3,
          },
          {
            group: "Papers",
            sales: "-",
            costOfIssue: 1300,
            costingPercentage: 4.3,
          },
        ],
      },
    ],

    totalRow: {
      group: "OVERALL",
      sales: 380000,
      costOfIssue: 152000,
      costingPercentage: 40,
    },
    expand: true,
    payload,
    _meta: null,
  };

  switch (resultType) {
    case ResultType.EXCEL:
      return createXlsxReport(result);
    default:
      return result;
  }
};

// item-wise stock movement
const getItemWiseStockMovementsReport = async (
  tenantId,
  payload,
  resultType
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.ITEM_WISE_STOCK_MOVEMENTS
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    openingAmount: 0,
    purchaseAmount: 0,
    transferInAmount: 0,
    transferOutAmount: 0,
    spoilageAmount: 0,
    adjustmentAmount: 0,
    consumptionAmount: 0,
    closingAmount: 0,
    varianceAmount: 0,
    expectedAmount: 0,
  };

  allDocs.forEach((data) => {

    const openingAmount = data?.opening?.totalValue ? paiseToRupee(data.opening.totalValue) : 0;
    const purchaseAmount = data?.purchase?.totalValue ? paiseToRupee(data.purchase.totalValue) : 0;
    const transferInAmount = data?.transferIn?.totalValue ? paiseToRupee(data.transferIn.totalValue) : 0;
    const transferOutAmount = data?.transferOut?.totalValue ? paiseToRupee(data.transferOut.totalValue) : 0;
    const spoilageAmount = data?.spoilage?.totalValue ? paiseToRupee(data.spoilage.totalValue) : 0;
    const adjustmentAmount = data?.adjustment?.totalValue ? paiseToRupee(data.adjustment.totalValue) : 0;
    const consumptionAmount = data?.consumption?.totalValue ? paiseToRupee(data.consumption.totalValue) : 0;
    const closingAmount = data?.physicalClosing?.totalValue ? paiseToRupee(data.physicalClosing.totalValue) : 0;
    const varianceAmount = data?.closingAdjustment?.totalValue ? paiseToRupee(data?.closingAdjustment?.totalValue) : 0;
    const expectedAmount = data?.systemClosing?.totalValue ? paiseToRupee(data.systemClosing.totalValue) : 0;

    const result = {
      itemName: data.inventoryItemName,
      itemCode: data.inventoryItemCode,
      businessDate: data.businessDate,
      location: data.locationName,
      workArea: data.inventoryLocationName,
      hsnCode: data?.hsnCode || "-",
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      pkg: data?.pkgName || "-",
      unitCost: data?.unitCost || 0,
      openingQty: data?.opening?.qty || 0,
      openingAmount,
      purchaseQty: data?.purchase?.qty || 0,
      purchaseAmount,
      transferInQty: data?.transferIn?.qty || 0,
      transferInAmount,
      transferOutQty: data?.transferOut?.qty || 0,
      transferOutAmount,
      spoilageQty: data?.spoilage?.qty || 0,
      spoilageAmount,
      adjustmentQty: data?.adjustment?.qty || 0,
      adjustmentAmount,
      consumptionQty: data?.consumption?.qty || 0,
      consumptionAmount,
      expectedQty: data?.systemClosing?.qty || 0,
      expectedAmount,
      closingQty: data?.physicalClosing?.qty || 0,
      closingAmount,
      varianceQty: data?.closingAdjustment?.qty || 0,
      varianceAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.openingAmount += openingAmount;
    report.result.totalRow.purchaseAmount += purchaseAmount;
    report.result.totalRow.transferInAmount += transferInAmount;
    report.result.totalRow.transferOutAmount += transferOutAmount;
    report.result.totalRow.spoilageAmount += spoilageAmount;
    report.result.totalRow.adjustmentAmount += adjustmentAmount;
    report.result.totalRow.consumptionAmount += consumptionAmount;
    report.result.totalRow.closingAmount += closingAmount;
    report.result.totalRow.varianceAmount += varianceAmount;
    report.result.totalRow.expectedAmount += expectedAmount;

    report.result.totalRow.totalValue += data.totalValue || 0;
  });

  return report.generate(resultType);
};

// Short supply Report
// requested vs issued
const getShortSupplyReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.SHORT_SUPPLY
  );
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };
  snapshot.forEach((doc) => {
    const data = doc.data();
    data.items.forEach((item) => {
      const result = {
        transferNo: data.transferNumber,
        itemName: item.itemName,
        itemCode: item.itemCode,
        pkg: item?.pkg.id === "default" ? item.purchaseUOM : item?.pkg.name,
        requestedQuantity: item.requestedQuantity,
        dispatchedQuantity: item.dispatchedQuantity || 0,
        shortageQuantity:
          item.requestedQuantity - (item.dispatchedQuantity || 0),
      };
      report.result.data.push(result);
    });
    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};

module.exports = {
  getTransferListReport,
  getDispatchTransferReport,
  getDetailedTransferReport,
  getCostOfIssueVsRevenueReport,
  getItemWiseStockMovementsReport,
  getShortSupplyReport,
};
