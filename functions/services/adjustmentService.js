const admin = require("firebase-admin");
const db = admin.firestore();

const schema = require("@/models/adjustmentSchema");
const {
  FirestoreDateHelper: FD,
  TIME_OPTION,
} = require("@/helpers/dateHelper");
const { handleValidation } = require("@/utils/validation");
const {
  saveAdjustment,
  getAllAdjustments,
  getById,
  updateById,
} = require("@/repositories/adjustmentRepo");
const { getNextAdjustmentId } = require("./counterService");
const { LedgerTypes, StockLedgerReasonCode } = require("@/defs/ledgerDefs");
const { creditStock, debitStock } = require("./stockService");
const { applyLedgersToDailyStock } = require("./stockMovementsService");
const { rupeeToPaise } = require("@/utils/money");

const createAdjustment = async (data) => {
  try {
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return;

    const adjustmentNumber = await getNextAdjustmentId(data.tenantId);

    const ledgers = [];

    const result = await db.runTransaction(async (trans) => {
      // CREDIT ITEMS
      for (const item of validatedData.items.filter(
        (i) => i.adjustmentType == "addition"
      )) {
        const uom =
          item.pkg && item.pkg.id !== "default"
            ? item.pkg.name
            : item.purchaseUOM; // purchaseUOM required

        const qty = item.adjustmentQuantity;
        const unitCost = rupeeToPaise(item.unitCost);
        const ledger = await creditStock(
          {
            ledgerType: LedgerTypes.ADJUSTMENT,
            reasonCode: StockLedgerReasonCode.MANUAL_CORRECTION,
            tenantId: validatedData.tenantId,
            locationId: validatedData.locationId,
            locationName: validatedData.locationName,
            inventoryLocationId: validatedData.workAreaId,
            inventoryLocationName: validatedData.workAreaName,
            itemId: item.itemId,
            itemCode: item.itemCode,
            itemName: item.itemName,
            qty,
            pkgUOM: uom,
            // countingUOM: item.countingUOM,
            recipeUOM: item.recipeUOM || "",
            conversionFactor: item.conversionFactor || 1,
            unitCost: unitCost || 0,
            totalCost: (unitCost || 0) * qty,
            expiryDate: item.expiryDate || null,
            grnMeta: null,
            categoryId: item.categoryId,
            subcategoryId: item.subcategoryId,
            categoryName: item.categoryName,
            subcategoryName: item.subcategoryName,
            pkg: item.pkg,
            remarks: item.remarks || null,
            eventDate: validatedData.adjustmentDate,
          },
          trans
        );

        ledgers.push(ledger);
      }

      // DEBIT ITEMS
      for (const item of validatedData.items.filter(
        (i) => i.adjustmentType == "reduction"
      )) {
        const uom =
          item.pkg && item.pkg.id !== "default"
            ? item.pkg.name
            : item.purchaseUOM; // purchaseUOM required

        const qty = item.adjustmentQuantity;
        const unitCost = rupeeToPaise(item.unitCost);

        const ledger = await debitStock(
          {
            ledgerType: LedgerTypes.ADJUSTMENT,
            reasonCode: StockLedgerReasonCode.MANUAL_CORRECTION,
            tenantId: validatedData.tenantId,
            locationId: validatedData.locationId,
            locationName: validatedData.locationName,
            inventoryLocationId: validatedData.workAreaId,
            inventoryLocationName: validatedData.workAreaName,
            itemId: item.itemId,
            itemCode: item.itemCode,
            itemName: item.itemName,
            qty,
            pkgUOM: uom,
            // countingUOM: item.countingUOM,
            unitCost: unitCost || 0,
            totalCost: (unitCost || 0) * qty,
            expiryDate: item.expiryDate || null,
            grnMeta: null,
            categoryId: item.categoryId,
            subcategoryId: item.subcategoryId,
            categoryName: item.categoryName,
            subcategoryName: item.subcategoryName,
            pkg: item.pkg,
            remarks: item.remarks || null,
            eventDate: validatedData.adjustmentDate,
          },
          trans
        );
        ledger["qty"] = -qty; // to perform negative stock movement
        ledger["totalCost"] = -ledger["totalCost"];
        ledgers.push(ledger);
      }

      const requestData = {
        ...validatedData,
        adjustmentNumber,
      };

      const adjustmentDoc = await saveAdjustment(requestData, trans);
      return adjustmentDoc;
    });

    applyLedgersToDailyStock({
      tenantId: validatedData.tenantId,
      locationId: validatedData.locationId,
      locationName: validatedData.locationName,
      inventoryLocationId: validatedData.workAreaId,
      inventoryLocationName: validatedData.workAreaName,
      eventDate: FD.toFirestore(
        validatedData.adjustmentDate,
        TIME_OPTION.START
      ),
      ledgers: ledgers,
    });

    return result;
  } catch (error) {
    console.error("Error in createAdjustment:", error);
    throw new Error(error.message);
  }
};

const getAdjustments = async (filters) => {
  const adjustments = await getAllAdjustments(filters);

  const result = adjustments.map((s) => {
    return {
      adjustmentNumber: s.adjustmentNumber,
      locationName: s.locationName,
      workAreaName: s.workAreaName,
      id: s.id,
      adjustmentDate: FD.toFormattedDate(s.adjustmentDate),
      requestedBy: s.requestedBy.name,
      requestedAt: FD.toFormattedDate(s.requestedBy.time),
    };
  });
  return result;
};

const getAdjustmentById = async (id) => {
  try {
    const data = await getById(id);
    if (!data) return null;
    return {
      ...data,
      adjustmentDate: FD.toFormattedDate(data.adjustmentDate),
      requestedBy: {
        ...data.requestedBy,
        time: FD.toFormattedDate(data.requestedBy.time),
      },
    };
  } catch (error) {
    throw Error(err.message);
  }
};

const updateAdjustment = async (id, payload) => {
  try {
    const data = handleValidation(payload, schema);
    if (!data) return;
    const result = await updateById(id, data);
    return result;
  } catch (error) {
    throw Error(err.message);
  }
};

module.exports = {
  createAdjustment,
  getAdjustments,
  getAdjustmentById,
  updateAdjustment,
};
