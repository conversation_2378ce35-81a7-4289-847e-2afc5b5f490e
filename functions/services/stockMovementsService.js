// Stock Movement Service
// ----------------------
// This file contains ONLY projection / derived logic.
// Source of truth is ALWAYS the stock ledger.
//
// Responsibilities:
// - Initialize daily stock (carry forward)
// - Apply movement blocks deterministically
// - Recalculate system & final closing
//
// NON-Responsibilities:
// - Writing ledger entries
// - Scheduling jobs
// - Business permissions

const {
  FirestoreDateHelper: FD,
  TIME_OPTION,
  dateStringToJsDate,
} = require("@/helpers/dateHelper");
const { tenantDoc } = require("@/helpers/tenantFirestore");
const { getFirestore, FieldPath } = require("firebase-admin/firestore");

const db = getFirestore();

// Reuse central ledger definitions
const { LedgerTypes } = require("@/defs/ledgerDefs");

// Map ledger types to daily movement buckets
// Using LedgerTypes enum avoids string drift
const LEDGER_TYPE_TO_MOVEMENT = {
  [LedgerTypes.GRN]: "purchase",
  [LedgerTypes.CONSUMPTION]: "consumption",
  [LedgerTypes.TRANSFER_IN]: "transferIn",
  [LedgerTypes.TRANSFER_OUT]: "transferOut",
  [LedgerTypes.SPOILAGE]: "spoilage",
  [LedgerTypes.ADJUSTMENT]: "adjustment",
};

// --------------------------------------------------
// Utility helpers
// --------------------------------------------------

// Debug helper to log how a movement changes a block
function logMovement(type, before, increment, after) {
  console.log({
    movementType: type,
    before,
    increment,
    after,
  });
}

function getBusinessDate(eventDate) {
  return FD.toFormattedDate(eventDate, "DD-MMM-YYYY");
}

// Safely extract quantity from a movement block
function q(block) {
  return Number(block?.qty || 0);
}

function parseRecipeQty(block) {
  return Number(block?.recipeQty || 0);
}

// Safely extract value from a movement block
function value(block) {
  return Number(block?.totalValue || 0);
}

// Firestore limits "in" to max 10 values per query
// So we also need batching if items.length > 10
function chunk(arr, size = 10) {
  const out = [];
  for (let i = 0; i < arr.length; i += size) {
    out.push(arr.slice(i, i + size));
  }
  return out;
}

async function fetchByDocIds(ref, ids = []) {
  if (!ids.length) return ref.get();

  const chunks = chunk(ids, 10);
  const snaps = await Promise.all(
    chunks.map((c) => ref.where(FieldPath.documentId(), "in", c).get())
  );

  return snaps.flatMap((s) => s.docs);
}

function calculateVariance(physicalClosing, systemClosing) {
  if (!physicalClosing) {
    physicalClosing = { qty: 0, recipeQty: 0, totalValue: 0 };
  }

  const unitValue =
    physicalClosing.qty === 0
      ? 0
      : physicalClosing.totalValue / physicalClosing.qty;

  const adjustmentQty = Number(
    (physicalClosing.qty - systemClosing.qty).toFixed(3)
  );
  const adjustmentRecipeQty = Number(
    (physicalClosing.recipeQty - systemClosing.recipeQty).toFixed(3)
  );
  const adjustmentValue = Number((adjustmentQty * unitValue).toFixed(2));

  return {
    adjustmentQty,
    adjustmentRecipeQty,
    adjustmentValue,
  };
}

// --------------------------------------------------
// Pure calculation helpers
// --------------------------------------------------

// Calculate system / final closing based on all movements
// This function is PURE and deterministic
function calculateClosing(movements = {}, uom) {
  const closingQty =
    q(movements.opening) +
    q(movements.purchase) +
    q(movements.transferIn) -
    q(movements.transferOut) -
    q(movements.consumption) -
    q(movements.spoilage) +
    q(movements.adjustment);

  const closingQtyInRecipeUOM =
    parseRecipeQty(movements.opening) +
    parseRecipeQty(movements.purchase) +
    parseRecipeQty(movements.transferIn) -
    parseRecipeQty(movements.transferOut) -
    parseRecipeQty(movements.consumption) -
    parseRecipeQty(movements.spoilage) +
    parseRecipeQty(movements.adjustment);

  const totalValue =
    value(movements.opening) +
    value(movements.purchase) +
    value(movements.transferIn) -
    value(movements.transferOut) -
    value(movements.consumption) -
    value(movements.spoilage) +
    value(movements.adjustment);

  return {
    qty: Number(closingQty.toFixed(3)) || 0,
    recipeQty: Number(closingQtyInRecipeUOM.toFixed(3)) || 0,
    uom,
    totalValue: Number(totalValue.toFixed(2)) || 0,
  };
}

function calculateFinalClosing(prev, current = { qty: 0, totalValue: 0 }) {
  return {
    qty: Number((prev.qty + current.qty).toFixed(3)),
    uom: prev.uom,
    totalValue: Number((prev.totalValue + current.totalValue).toFixed(2)),
  };
}

// Add two movement blocks together safely
// Used when the same movement type happens multiple times a day
function addMovement(current = {}, increment = {}) {
  const qty = Number((q(current) + q(increment)).toFixed(3));
  const recipeQty = Number(
    (parseRecipeQty(current) + parseRecipeQty(increment)).toFixed(3)
  );
  const totalValue = Number((value(current) + value(increment)).toFixed(2));

  return {
    qty,
    recipeQty,
    uom: increment.uom || current.uom,
    totalValue,
  };
}

// --------------------------------------------------
// Daily projection mutation
// --------------------------------------------------

// Apply one movement block to the current daily projection
// Recalculates systemClosing & finalClosing every time
function applyMovement({
  currentItem = {},
  movementType, // purchase | consumption | transferIn | ...
  increment, // { qty, uom, totalValue }
}) {
  const updated = { ...currentItem };

  const before = currentItem[movementType] || {};
  const after = addMovement(before, increment);

  logMovement(movementType, before, increment, after);

  updated[movementType] = after;

  // Deterministic recalculation
  const closing = calculateClosing(updated, after.uom);
  updated.systemClosing = closing;
  if (!updated.physicalClosing?.qty) {
    updated.finalClosing = closing;
  } else {
    updated.finalClosing = updated.physicalClosing;
  }

  const { adjustmentQty, adjustmentRecipeQty, adjustmentValue } =
    calculateVariance(updated.physicalClosing, updated.systemClosing);

  updated.closingAdjustment = {
    qty: adjustmentQty,
    recipeQty: adjustmentRecipeQty,
    uom: updated.closingAdjustment.uom,
    totalValue: adjustmentValue,
  };

  updated.updatedAt = FD.now();

  return updated;
}

// --------------------------------------------------
// Daily projection initialization
// --------------------------------------------------

// Create a new empty daily stock movement record
// No movements are initialized here
function newStockMovementRecord({
  tenantId,
  locationId,
  locationName,
  inventoryLocationId,
  inventoryLocationName,
  inventoryItemId,
  inventoryItemCode,
  inventoryItemName,
  pkgId,
  pkgName,
  categoryId,
  categoryName,
  subcategoryId,
  subcategoryName,
  businessDate,
}) {
  // Initialize all movement buckets with zero values
  // This keeps the document shape stable and avoids undefined checks later
  const zeroBlock = (uom = "") => ({
    qty: 0,
    recipeQty: 0,
    uom,
    totalValue: 0,
  });

  return {
    tenantId,
    locationId,
    locationName,

    inventoryLocationId,
    inventoryLocationName,

    inventoryItemId,
    inventoryItemCode,
    inventoryItemName,
    pkgId,
    pkgName,
    categoryId,
    categoryName,
    subcategoryId,
    subcategoryName,

    businessDate,

    // Movement buckets (initialized)
    opening: zeroBlock(),
    purchase: zeroBlock(),
    transferIn: zeroBlock(),
    transferOut: zeroBlock(),
    consumption: zeroBlock(),
    adjustment: zeroBlock(),
    spoilage: zeroBlock(),

    // Closings (initialized)
    systemClosing: zeroBlock(),
    physicalClosing: zeroBlock(), // physical count may or may not happen
    closingAdjustment: zeroBlock(),
    finalClosing: zeroBlock(),

    createdAt: FD.now(),
    updatedAt: FD.now(),
  };
}

// --------------------------------------------------
// Carry forward logic (internal / private)
// --------------------------------------------------

// Carry forward stock for a SINGLE work area
// This is the core logic and should not be called directly by jobs
async function carryForwardForWorkArea({
  tenantId,
  locationId,
  locationName,
  inventoryLocationId,
  inventoryLocationName,
  businessDate,
  fallbackMode = "ZERO",
}) {
  const startTime = Date.now();

  try {
    const today = businessDate;
    const todayInJsDate = dateStringToJsDate(today);
    const yesterday = getBusinessDate(
      FD.subtractDays(FD.toFirestore(todayInJsDate), 1)
    );

    const baseRef = tenantDoc(tenantId)
      .collection("locations")
      .doc(locationId)
      .collection("workAreas")
      .doc(inventoryLocationId)
      .collection("stockMovements");

    const todayItemsRef = baseRef.doc(today).collection("items");
    const yesterdayItemsRef = baseRef.doc(yesterday).collection("items");

    const yesterdaySnap = await yesterdayItemsRef.get();

    if (yesterdaySnap.empty) {
      return {
        status: "SUCCESS",
        affectedItems: 0,
        source: "NO_PREVIOUS_DATA",
        durationMs: Date.now() - startTime,
      };
    }

    const todaySnap = await todayItemsRef.limit(1).get();
    const isTodayEmpty = todaySnap.empty;

    const yesterdayMap = new Map(
      yesterdaySnap.docs.map((d) => [d.id, d.data()])
    );

    // 🟢 Case 1 — Today does not exist: create it from yesterday
    if (isTodayEmpty) {
      let batch = db.batch();
      let count = 0;

      for (const [docId, prev] of yesterdayMap.entries()) {
        const opening = prev.finalClosing || {
          qty: 0,
          recipeQty: 0,
          uom: prev.opening?.uom,
          totalValue: 0,
        };

        const todayRecord = newStockMovementRecord({
          tenantId,
          locationId,
          locationName: prev.locationName,
          inventoryLocationId,
          inventoryLocationName: prev.inventoryLocationName,
          inventoryItemId: prev.inventoryItemId,
          inventoryItemCode: prev.inventoryItemCode,
          inventoryItemName: prev.inventoryItemName,
          pkgId: prev.pkgId,
          pkgName: prev.pkgName,
          categoryId: prev.categoryId,
          categoryName: prev.categoryName,
          subcategoryId: prev.subcategoryId,
          subcategoryName: prev.subcategoryName,
          businessDate: today,
        });

        todayRecord.opening = { ...opening };
        todayRecord.systemClosing = { ...opening };
        todayRecord.finalClosing = { ...opening };
        todayRecord.carriedFromDate = yesterday;
        todayRecord.source = "CARRY_FORWARD";
        todayRecord.updatedAt = FD.now();

        batch.set(todayItemsRef.doc(docId), todayRecord, { merge: false });

        if (++count === 450) {
          await batch.commit();
          batch = db.batch();
          count = 0;
        }
      }

      if (count > 0) await batch.commit();

      return {
        status: "CREATED",
        affectedItems: yesterdayMap.size,
        source: yesterday,
        durationMs: Date.now() - startTime,
      };
    }

    // 🔵 Case 2 — Today exists: sync opening then recalc forward

    const todayDocsSnap = await todayItemsRef.get();
    const todayMap = new Map(todayDocsSnap.docs.map((d) => [d.id, d]));

    let batch = db.batch();
    let batchCount = 0;

    for (const [docId, prev] of yesterdayMap.entries()) {
      const todayDoc = todayMap.get(docId);
      if (!todayDoc) continue;

      const opening = prev.finalClosing || {
        qty: 0,
        recipeQty: 0,
        uom: prev.opening?.uom,
        totalValue: 0,
      };

      batch.update(todayDoc.ref, {
        opening: { ...opening },
        carriedFromDate: yesterday,
        source: "CARRY_FORWARD_RESYNC",
        updatedAt: FD.now(),
      });

      if (++batchCount === 450) {
        await batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    }

    if (batchCount > 0) await batch.commit();

    // Now recalc forward
    await recalculateStockFromDate({
      tenantId,
      locationId,
      inventoryLocationId,
      startDate: FD.toFirestore(todayInJsDate),
      items: [...yesterdayMap.keys()],
    });

    return {
      status: "RECALCULATED",
      affectedItems: yesterdayMap.size,
      source: yesterday,
      durationMs: Date.now() - startTime,
    };
  } catch (err) {
    console.error("CarryForwardForWorkArea failed", {
      tenantId,
      locationId,
      inventoryLocationId,
      businessDate,
      error: err.message,
      stack: err.stack,
    });

    return {
      status: "FAILED",
      error: err.message,
      durationMs: Date.now() - startTime,
    };
  }
}

// --------------------------------------------------
// Carry forward entry point
// --------------------------------------------------

// Entry point to carry forward stock for ALL locations & work areas
// This is the function that jobs / APIs should call
async function carryForwardStock({
  tenantId,
  eventDate,
  fallbackMode = "ZERO",
}) {
  const tenantRef = tenantDoc(tenantId);
  const businessDate = getBusinessDate(eventDate);

  const locationsRef = await tenantRef.collection("locations").listDocuments();

  const locations = locationsRef.map((doc) => doc.id);

  const results = [];

  for (const location of locations) {
    const workAreasRef = await tenantRef
      .collection("locations")
      .doc(location)
      .collection("workAreas")
      .listDocuments();

    const workAreas = workAreasRef.map((doc) => doc.id);

    for (const workArea of workAreas) {
      const result = await carryForwardForWorkArea({
        tenantId,
        locationId: location,
        inventoryLocationId: workArea,
        businessDate,
        fallbackMode,
      });

      results.push({
        locationId: location,
        workAreaId: workArea,
        ...result,
      });
    }
  }

  return {
    status: "DONE",
    businessDate,
    results,
  };
}

// --------------------------------------------------
// Ledger application logic
// --------------------------------------------------

// Apply a single ledger entry to one item's daily stock projection
// Internal helper – not exported
async function applyLedgerToItemDailyStock({
  tenantId,
  locationId,
  locationName,
  inventoryLocationId,
  inventoryLocationName,
  businessDate,
  ledger,
}) {
  const movementType = LEDGER_TYPE_TO_MOVEMENT[ledger.ledgerType];

  if (!movementType) {
    throw new Error(`Unsupported ledgerType: ${ledger.ledgerType}`);
  }

  const itemRef = tenantDoc(tenantId)
    .collection("locations")
    .doc(locationId)
    .collection("workAreas")
    .doc(inventoryLocationId)
    .collection("stockMovements")
    .doc(businessDate)
    .collection("items")
    .doc(`${ledger.itemId}_${ledger.pkg.id}`);

  const snap = await itemRef.get();

  let currentItem;

  if (!snap.exists) {
    // Create a fresh daily projection if missing
    currentItem = newStockMovementRecord({
      tenantId,
      locationId,
      locationName,
      inventoryLocationId,
      inventoryLocationName,
      inventoryItemId: ledger.itemId,
      inventoryItemCode: ledger.itemCode,
      inventoryItemName: ledger.itemName,
      pkgId: ledger.pkg?.id,
      pkgName: ledger.pkg?.name,
      businessDate,
      categoryId: ledger.categoryId,
      categoryName: ledger.categoryName,
      subcategoryId: ledger.subcategoryId,
      subcategoryName: ledger.subcategoryName,
    });
  } else {
    currentItem = snap.data();
  }

  const updated = applyMovement({
    currentItem,
    movementType,
    increment: {
      qty: ledger.qty,
      recipeQty: ledger.remainingQtyInRecipeUOM,
      uom: ledger.pkgUOM,
      totalValue: ledger.totalCost,
    },
  });

  await itemRef.set(updated, { merge: true });
}

// Entry point: apply multiple ledger entries to daily stock
// Called AFTER a business action (GRN, transfer, consumption) is committed
async function applyLedgersToDailyStock({
  tenantId,
  locationId,
  locationName,
  inventoryLocationId,
  inventoryLocationName,
  eventDate,
  ledgers = [],
}) {
  const businessDate = getBusinessDate(eventDate);
  const startTime = Date.now();
  const results = [];

  for (const ledger of ledgers) {
    try {
      await applyLedgerToItemDailyStock({
        tenantId,
        locationId,
        locationName,
        inventoryLocationId,
        inventoryLocationName,
        businessDate,
        ledger,
      });

      results.push({
        ledgerId: ledger.id,
        inventoryItemId: ledger.itemId,
        inventoryItemName: ledger.itemName,
        pkgId: ledger.pkg.id,
        pkgName: ledger.pkg.name,
        status: "SUCCESS",
      });
    } catch (err) {
      console.error("applyLedgerToItemDailyStock failed", {
        tenantId,
        locationId,
        inventoryLocationId,
        businessDate,
        ledgerId: ledger.id,
        error: err.message,
        stack: err.stack,
      });

      results.push({
        ledgerId: ledger.id,
        inventoryItemId: ledger.itemId,
        inventoryItemName: ledger.itemName,
        pkgId: ledger.pkg.id,
        pkgName: ledger.pkg.name,
        status: "FAILED",
        error: err.message,
      });
    }
  }

  const affectedItemIds = [
    ...new Set(ledgers.map((l) => `${l.itemId}_${l.pkg.id}`).filter(Boolean)),
  ];

  await recalculateStockFromDate({
    tenantId,
    locationId,
    inventoryLocationId,
    startDate: eventDate,
    items: affectedItemIds,
  });

  return {
    status: "DONE",
    businessDate,
    processed: results.length,
    durationMs: Date.now() - startTime,
    results,
  };
}

// --------------------------------------------------
// Physical stock closing & recalculation
// --------------------------------------------------

// Apply physical stock closing for MULTIPLE items for a business date
// This records observed physicalClosing per item and derives closingAdjustment
// NOTE: Adjustment ledgers must already be written before calling this
async function applyPhysicalClosing(
  {
    tenantId,
    locationId,
    locationName,
    inventoryLocationId,
    inventoryLocationName,
    eventDate,
    items = [],
  },
  stockCorrection = true
) {
  const businessDate = getBusinessDate(eventDate);
  const startTime = Date.now();
  const results = [];

  for (const item of items) {
    try {
      const itemRef = tenantDoc(tenantId)
        .collection("locations")
        .doc(locationId)
        .collection("workAreas")
        .doc(inventoryLocationId)
        .collection("stockMovements")
        .doc(businessDate)
        .collection("items")
        .doc(`${item.itemId}_${item.pkg.id}`);

      const snap = await itemRef.get();
      let currentItem;

      if (!snap.exists) {
        // Create a fresh daily projection if missing
        currentItem = newStockMovementRecord({
          tenantId,
          locationId,
          locationName,
          inventoryLocationId,
          inventoryLocationName,
          inventoryItemId: item.itemId,
          inventoryItemCode: item.itemCode,
          inventoryItemName: item.itemName,
          pkgId: item.pkg?.id,
          pkgName: item.pkg?.name,
          businessDate,
          categoryId: item.categoryId,
          categoryName: item.categoryName,
          subcategoryId: item.subcategoryId,
          subcategoryName: item.subcategoryName,
        });
      } else {
        currentItem = snap.data();
      }

      const physicalQty = Number(item.qty.toFixed(3));
      const physicalRecipeQty = Number(item.qtyInRecipeUOM.toFixed(3));
      const physicalValue = Number(item.totalCost.toFixed(2));

      currentItem.physicalClosing = {
        qty: physicalQty,
        recipeQty: physicalRecipeQty,
        uom: item.uom,
        totalValue: physicalValue,
      };
      if (stockCorrection) {
        const { adjustmentQty, adjustmentRecipeQty, adjustmentValue } =
          calculateVariance(
            currentItem.physicalClosing,
            currentItem.systemClosing
          );

        currentItem.closingAdjustment = {
          qty: adjustmentQty,
          recipeQty: adjustmentRecipeQty,
          uom: item.uom,
          totalValue: adjustmentValue,
        };

        currentItem.finalClosing = {
          qty: physicalQty,
          recipeQty: physicalRecipeQty,
          uom: item.uom,
          totalValue: physicalValue,
        };
      }

      currentItem.updatedAt = FD.now();
      await itemRef.set(currentItem, { merge: true });

      results.push({
        inventoryItemId: item.itemId,
        pkgId: item.pkg.id,
        status: "SUCCESS",
      });
    } catch (err) {
      console.log(err, "error");
      results.push({
        inventoryItemId: item.itemId,
        pkgId: item.pkg.id,
        status: "FAILED",
        error: err.message,
      });
    }
  }

  return {
    status: "DONE",
    businessDate,
    durationMs: Date.now() - startTime,
    results,
  };
}

// Recalculate daily stock from a given date forward for selected items
// Used when backdated ledger or physical closing happens
async function recalculateStockFromDate({
  tenantId,
  locationId,
  inventoryLocationId,
  startDate,
  items = [],
}) {
  const today = FD.now(TIME_OPTION.START);
  const nextDay = FD.addDays(today, 1);

  const baseRef = tenantDoc(tenantId)
    .collection("locations")
    .doc(locationId)
    .collection("workAreas")
    .doc(inventoryLocationId)
    .collection("stockMovements");

  let currentDate = startDate;

  while (FD.compareTimestamps(currentDate, nextDay) <= 0) {
    const businessDate = getBusinessDate(currentDate);
    const prevBusinessDate = getBusinessDate(FD.subtractDays(currentDate, 1));

    const todayRef = baseRef.doc(businessDate).collection("items");
    const prevRef = baseRef.doc(prevBusinessDate).collection("items");

    const [todayDocs, prevDocs] = await Promise.all([
      fetchByDocIds(todayRef, items),
      fetchByDocIds(prevRef, items),
    ]);

    // Nothing to propagate from — move forward
    if (!prevDocs.length) {
      currentDate = FD.addDays(currentDate, 1);
      continue;
    }

    const prevMap = new Map(prevDocs.map((d) => [d.id, d.data()]));
    const todayMap = new Map(todayDocs.map((d) => [d.id, d]));

    let batch = db.batch();
    let batchCount = 0;

    // 🔹 Create missing today items
    for (const [docId, prev] of prevMap.entries()) {
      if (!todayMap.has(docId)) {
        const opening = prev.finalClosing || {
          qty: 0,
          recipeQty: 0,
          uom: prev.opening?.uom,
          totalValue: 0,
        };

        const todayRecord = newStockMovementRecord({
          tenantId,
          locationId,
          locationName: prev.locationName,
          inventoryLocationId,
          inventoryLocationName: prev.inventoryLocationName,
          inventoryItemId: prev.inventoryItemId,
          inventoryItemCode: prev.inventoryItemCode,
          inventoryItemName: prev.inventoryItemName,
          pkgId: prev.pkgId,
          pkgName: prev.pkgName,
          categoryId: prev.categoryId,
          categoryName: prev.categoryName,
          subcategoryId: prev.subcategoryId,
          subcategoryName: prev.subcategoryName,
          businessDate,
        });

        // Overwrite opening & closings from yesterday
        todayRecord.opening = { ...opening };
        todayRecord.systemClosing = { ...opening };
        todayRecord.finalClosing = { ...opening };
        todayRecord.source = "RECALCULATE_CARRY_FORWARD";
        todayRecord.updatedAt = FD.now();

        batch.set(todayRef.doc(docId), todayRecord, { merge: false });
        batchCount++;
      }
    }

    // 🔹 Recalculate existing today items
    for (const doc of todayDocs) {
      const todayItem = doc.data();
      const prevItem = prevMap.get(doc.id);

      if (prevItem?.finalClosing) {
        todayItem.opening = { ...prevItem.finalClosing };
      }

      const closing = calculateClosing(todayItem, todayItem.opening?.uom);
      todayItem.systemClosing = closing;

      const { adjustmentQty, adjustmentRecipeQty, adjustmentValue } =
        calculateVariance(todayItem.physicalClosing, todayItem.systemClosing);

      todayItem.closingAdjustment = {
        qty: adjustmentQty,
        recipeQty: adjustmentRecipeQty,
        uom: todayItem.closingAdjustment?.uom,
        totalValue: adjustmentValue,
      };

      todayItem.finalClosing = calculateFinalClosing(
        closing,
        todayItem.closingAdjustment
      );
      todayItem.updatedAt = FD.now();

      batch.set(doc.ref, todayItem, { merge: true });
      batchCount++;
    }

    if (batchCount > 0) await batch.commit();

    currentDate = FD.addDays(currentDate, 1);
  }

  return { status: "SUCCESS", startDate };
}

// --------------------------------------------------
// Exports
// --------------------------------------------------

module.exports = {
  newStockMovementRecord,
  applyMovement,
  carryForwardStock,
  applyLedgersToDailyStock,
  applyPhysicalClosing,
  recalculateStockFromDate,
  calculateClosing,
};
