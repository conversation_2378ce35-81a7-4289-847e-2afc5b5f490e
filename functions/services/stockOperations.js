// services/stockOperations.js
// Shared stock operations to avoid circular dependencies

const StockLedgerRepo = require("@/repositories/stockLedgerRepo");
const { LedgerTypes } = require("@/defs/ledgerDefs");

/**
 * Helper function to calculate quantity from recipe quantity
 * @param {Object} batch - The batch data
 * @param {string} pkgSymbol - Package symbol/UOM
 * @param {number} recipeQty - Quantity in recipe UOM
 * @returns {number} Quantity in package UOM
 */
const calculateQuantityFromRecipeQty = (batch, pkgSymbol, recipeQty) => {
  // Case 1: using purchase unit as package UOM
  if (batch.purchaseUOM === pkgSymbol) {
    // If we have conversion info in the batch
    if (batch.recipeQty && batch.qty) {
      // Calculate conversion factor from batch data
      const conversionFactor = batch.recipeQty / batch.qty;
      return recipeQty / conversionFactor;
    }
    return recipeQty;
  }

  // Case 2: using a package unit
  const currentPkg = batch.pkg;
  
  if (!currentPkg) {
    throw new Error(`Package data not found in batch`);
  }
  
  // If the package unit has a conversion to the recipe unit
  if (currentPkg.toUnit === batch.recipeUOM && currentPkg.quantity) {
    return recipeQty / currentPkg.quantity;
  }
  
  // Fallback: use batch data to calculate conversion
  if (batch.recipeQty && batch.qty) {
    const conversionFactor = batch.recipeQty / batch.qty;
    return recipeQty / conversionFactor;
  }
  
  return recipeQty;
};

/**
 * Debits stock by recipe quantity using FIFO method
 * @param {Object} payload - Debit payload
 * @param {Object} t - Firestore transaction
 * @param {Function} calculateAggregateQtyFn - Function to calculate aggregate quantity
 * @param {Function} debitStockFn - Function to debit stock
 * @returns {Object} Debit results
 */
exports.debitStockByRecipeQty = async function (payload, t, calculateAggregateQtyFn, debitStockFn) {
  const { itemId, qtyInRecipeUOM, inventoryLocationId } = payload;
  
  const [aggregateResult] = await calculateAggregateQtyFn(
    inventoryLocationId,
    [{ itemId }]
  );
  
  if (aggregateResult.inStock < qtyInRecipeUOM) {
    throw new Error(
      `Insufficient stock for item ${payload.itemName || itemId}. ` +
      `Required: ${qtyInRecipeUOM} (recipe UOM), ` +
      `Available: ${aggregateResult.inStock} (recipe UOM)`
    );
  }

  const ledgerBatches = await StockLedgerRepo.getAvailableBatches(
    itemId,
    inventoryLocationId,
    null,
    t
  );

  if (!ledgerBatches || ledgerBatches.length === 0) {
    throw new Error(`No available batches found for item ${itemId}`);
  }

  let remainingQtyToDebit = qtyInRecipeUOM;
  const batchesToDebit = [];

  for (const batch of ledgerBatches) {
    if (remainingQtyToDebit <= 0) break;

    const qtyToDebitFromBatch = Math.min(
      batch.remainingQtyInRecipeUOM,
      remainingQtyToDebit
    );

    batchesToDebit.push({
      ...batch,
      qtyToDebit: qtyToDebitFromBatch,
      newRemainingQtyInRecipeUOM: batch.remainingQtyInRecipeUOM - qtyToDebitFromBatch,
      newRemainingQty: batch.remainingQty - (qtyToDebitFromBatch / (batch.remainingQtyInRecipeUOM / batch.remainingQty))
    });

    remainingQtyToDebit -= qtyToDebitFromBatch;
  }

  // Process each batch and create debit entries
  const debitResults = [];
  
  for (const batch of batchesToDebit) {
    // Calculate the quantity to debit in the package UOM using batch data
    const qtyToDebitInPkgUOM = calculateQuantityFromRecipeQty(
      batch,
      batch.pkgUOM,
      batch.qtyToDebit
    );

    const debitPayload = {
      ledgerType: LedgerTypes.CONSUMPTION,
      tenantId: batch.tenantId,
      locationId: batch.locationId,
      locationName: batch.locationName,
      inventoryLocationId: batch.inventoryLocationId,
      inventoryLocationName: batch.inventoryLocationName,
      itemId: batch.itemId,
      itemCode: batch.itemCode,
      itemName: batch.itemName,
      qty: qtyToDebitInPkgUOM,
      qtyInRecipeUOM: batch.qtyToDebit,
      pkgUOM: batch.pkgUOM,
      unitCost: batch.unitCost,
      totalCost: batch.unitCost * qtyToDebitInPkgUOM,
      expiryDate: batch.expiryDate || null,
      grnMeta: null,
      categoryId: batch.categoryId,
      subcategoryId: batch.subcategoryId,
      categoryName: batch.categoryName,
      subcategoryName: batch.subcategoryName,
      pkg: batch.pkg,
      remarks: batch.remarks || null,
      eventDate: new Date()
    };

    const result = await debitStockFn(debitPayload, t);
    debitResults.push(result);
  }

  return {
    aggregateResult,
    batchesToDebit,
    debitResults,
    totalDebited: qtyInRecipeUOM
  };
};