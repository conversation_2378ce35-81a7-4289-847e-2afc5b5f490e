// services/stockService.js
const StockLedgerRepo = require("@/repositories/stockLedgerRepo");
const StockRepo = require("@/repositories/stockRepo");
const { StockTransactionType } = require("@/defs/ledgerDefs");
const { paiseToRupee, rupeeToPaise } = require("@/utils/money");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { getInventoryItem } = require("@/repositories/itemRepo");
const admin = require("firebase-admin");
const db = admin.firestore();

/**
 * Calculate the quantity of an item in recipe unit
 * given the quantity in a package unit
 * @param {Object} item - item object
 * @param {string} pkgSymbol - symbol of the package unit
 * @param {number} quantity - quantity of the item in the package unit
 * @returns {number} - quantity of the item in the recipe unit
 */
const calculateRecipeQty = (item, pkgSymbol, quantity) => {
  // Case 1: using purchase unit as package UOM
  if (item.purchaseUnit.symbol === pkgSymbol) {
    // If the purchase unit has a conversion to the recipe unit
    if (
      item.purchaseUnit.toUnit &&
      item.purchaseUnit.toUnit === item.recipeUnit.symbol
    ) {
      return quantity * item.purchaseUnit.quantity;
    }
    // No conversion, return the quantity as is
    return quantity;
  }

  // Case 2: using a package unit
  const currentPkg = item.packages.find((p) => p.name === pkgSymbol);
  // If the package unit has a conversion to the recipe unit
  if (currentPkg.toUnit === item.recipeUnit.symbol) {
    return quantity * currentPkg.quantity;
  }
  // If the purchase unit has a conversion to the recipe unit
  if (
    item.purchaseUnit.toUnit &&
    item.purchaseUnit.toUnit === item.recipeUnit.symbol
  ) {
    // Multiply the quantity by the conversion of the package unit and the purchase unit
    return quantity * currentPkg.quantity * item.purchaseUnit.quantity;
  }
  // fallback: package → purchaseUnit (no conversion to recipe)
  return (quantity * currentPkg.quantity) / item.purchaseUnit.quantity;
};

/**
 * Generic Credit function
 * @param {Object} payload {itemCode, inventoryLocationId, qty, unitCost, uom, ledgerType, referenceId}
 */
exports.creditStock = async function (payload, t) {
  const item = await getInventoryItem(payload.itemId);
  payload.purchaseUOM = item.purchaseUnit.symbol;
  payload.countingUOM = item.countingUnit.symbol;
  payload.recipeUOM = item.recipeUnit.symbol;
  payload.pkgQty = payload.qty;
  payload.itemPrice = rupeeToPaise(item.unitCost);
  payload.recipeQty = payload.qtyInRecipeUOM
    ? payload.qtyInRecipeUOM
    : calculateRecipeQty(item, payload.pkgUOM, payload.qty);
  payload.reasonCode = payload.reasonCode || null;
  // 1. Insert credit entry in ledger
  const ledger = await StockLedgerRepo.addEntry(
    {
      // basic
      tenantId: payload.tenantId,
      locationId: payload.locationId,
      locationName: payload.locationName,
      inventoryLocationId: payload.inventoryLocationId,
      inventoryLocationName: payload.inventoryLocationName,

      // item details
      itemId: payload.itemId,
      itemCode: payload.itemCode,
      itemName: payload.itemName,
      categoryId: payload.categoryId,
      subcategoryId: payload.subcategoryId,
      categoryName: payload.categoryName,
      subcategoryName: payload.subcategoryName,
      pkgUOM: payload.pkgUOM,
      purchaseUOM: payload.purchaseUOM,
      countingUOM: payload.countingUOM,
      recipeUOM: payload.recipeUOM,
      unitCost: payload.unitCost,
      totalCost: payload.totalCost,
      expiryDate: payload.expiryDate || null,
      discount: payload.discount || 0,
      taxRate: payload.taxRate || 0,
      taxAmount: payload.taxAmount || 0,
      foc: payload.foc || 0,
      cess: payload.cess || 0,
      remarks: payload.remarks,
      pkg: payload.pkg,

      // quantity
      orderedQty: payload.orderedQty || 0,
      qty: payload.qty,
      remainingQty: payload.qty,
      remainingQtyInRecipeUOM: payload.recipeQty,

      ledgerType: payload.ledgerType,
      transactionType: StockTransactionType.IN,
      reasonCode: payload.reasonCode, // @todo: validate reasoncode
      grnMeta: payload.grnMeta,
      eventDate: payload.eventDate || FD.now(),
    },
    t
  );

  // 2. Update Stock table
  await StockRepo.increaseStock(payload, t);

  return ledger;
};

exports.debitStock = async function (payload) {
  const item = await getInventoryItem(payload.itemId);

  const ledgerBatches = await StockLedgerRepo.getAvailableBatches(
    payload.itemId,
    payload.inventoryLocationId,
    payload.pkg?.id || "default",
    null
  );

  let totalValue = 0;
  let totalPkgDeducted = 0;
  let totalRecipeDeducted = 0;
  let newLedger = null; // 👈 capture created ledger

  await db.runTransaction(async (t) => {
    const recipeQtyToDeduct = payload.qtyInRecipeUOM
      ? payload.qtyInRecipeUOM
      : calculateRecipeQty(item, payload.pkgUOM, payload.qty);

    if (!payload.qty) {
      const factor = calculateRecipeQty(item, payload.pkgUOM, 1);
      payload.qty = payload.qtyInRecipeUOM / factor;
    }

    let pkgQtyLeft = payload.qty;
    let recipeQtyLeft = recipeQtyToDeduct;

    for (const batch of ledgerBatches) {
      if (pkgQtyLeft <= 0 || recipeQtyLeft <= 0) break;

      const deductPkgQty = Math.min(batch.remainingQty, pkgQtyLeft);
      const deductRecipeQty = Math.min(
        batch.remainingQtyInRecipeUOM,
        recipeQtyLeft
      );

      await StockLedgerRepo.updateRemaining(
        batch.id,
        batch.remainingQty - deductPkgQty,
        batch.remainingQtyInRecipeUOM - deductRecipeQty,
        t
      );

      batch.remainingQty -= deductPkgQty;
      batch.remainingQtyInRecipeUOM -= deductRecipeQty;

      const price = deductPkgQty * batch.unitCost;
      const costWithTax = price + price * ((batch.taxRate || 0) / 100);

      totalValue += costWithTax;
      totalPkgDeducted += deductPkgQty;
      totalRecipeDeducted += deductRecipeQty;

      pkgQtyLeft -= deductPkgQty;
      recipeQtyLeft -= deductRecipeQty;
    }

    // 🚨 Guard: insufficient stock
    if (pkgQtyLeft > 0 && recipeQtyLeft > 0) {
      throw new Error(
        `Insufficient stock for item ${payload.itemName}. 
         Required: ${payload.qty} pkg / ${recipeQtyToDeduct} recipe, 
         Available: ${totalPkgDeducted} pkg / ${totalRecipeDeducted} recipe`
      );
    }

    // ✅ Single WAC OUT ledger
    newLedger = await StockLedgerRepo.addEntry(
      {
        ...payload,
        transactionType: StockTransactionType.OUT,
        qty: totalPkgDeducted,
        qtyInRecipeUOM: totalRecipeDeducted,
        unitCost: totalPkgDeducted ? totalValue / totalPkgDeducted : 0, // effective WAC
        taxRate: null,
        ledgerRefId: "WAC",
        eventDate: payload.eventDate || FD.now(),
        totalCost: totalValue,
      },
      t
    );

    await StockRepo.decreaseStock(
      {
        itemId: payload.itemId,
        inventoryLocationId: payload.inventoryLocationId,
        packageId: payload.pkg?.id,
        qty: totalPkgDeducted,
        recipeQty: totalRecipeDeducted,
        totalValue,
      },
      t
    );
  });

  // 🔁 return the created ledger
  return newLedger;
};

exports.aggregateStocks = async function (tenantId, filters) {
  const stocks = await StockRepo.getStocks(tenantId, filters);
  if (!stocks.length) return [];

  // ----------- SIMPLE UOM CONVERTER FOR OPEN QTY ----------
  function convertOpenQty(qty, from, to) {
    if (from === to) return qty;

    if (from === "kg" && to === "g") return qty * 1000;
    if (from === "g" && to === "kg") return qty / 1000;

    if (from === "l" && to === "ml") return qty * 1000;
    if (from === "ml" && to === "l") return qty / 1000;

    return qty; // fallback if no mapping
  }

  // ----------------------------------------------------------

  const items = {};

  for (const stock of stocks) {
    const key = `${stock.itemId}_${stock.inventoryLocationId}`;

    if (!items[key]) {
      items[key] = {
        meta: {
          itemId: stock.itemId,
          itemName: stock.itemName,
          itemCode: stock.itemCode,
          locationName: stock.locationName,
          inventoryLocationName: stock.inventoryLocationName,
          categoryName: stock.categoryName,
          subCategoryName: stock.subcategoryName,
          uom: stock.countingUOM, // OPEN qty UOM
          purchaseUOM: stock.purchaseUOM, // needed for conversion
        },
        packages: {}, // add workArea wise stock information if needed
        openQty: 0,
        openCost: 0,
      };
    }

    const group = items[key];

    const pkg = stock.pkg;
    const pkgQty = stock.pkgQty;
    const unitCountPerPackage = pkg.quantity;
    const totalValue = stock.totalValue;

    // ---------- DEFAULT PACKAGE ADJUSTMENT ----------
    let effectiveSize = unitCountPerPackage;
    if (pkg.id === "default") {
      effectiveSize = 1; // default package = 1 kg unit
    }
    // ------------------------------------------------

    const totalBase = pkgQty * effectiveSize;
    const costPerUnit = totalValue / totalBase;

    const wholePackages = Math.floor(pkgQty);
    const wholeUnits = wholePackages * effectiveSize;
    const wholeCost = wholeUnits * costPerUnit;

    const fractionalPart = pkgQty - wholePackages;

    let rawOpenUnits =
      Math.round(fractionalPart * effectiveSize * 1000000) / 1000000;
    const rawOpenCost = rawOpenUnits * costPerUnit;

    // -------- STORE FULL PACKAGE ROW --------
    if (!group.packages[pkg.name]) {
      group.packages[pkg.name] = {
        entryType: "Package",
        packageName: pkg.name,
        qty: 0,
        totalCost: 0,
      };
    }

    group.packages[pkg.name].qty += wholePackages;
    group.packages[pkg.name].totalCost += wholeCost;

    // -------- STORE OPEN PACKAGE (still in purchaseUOM here) --------
    if (pkg.id === "default") {
      rawOpenUnits = convertOpenQty(
        rawOpenUnits,
        stock.purchaseUOM,
        stock.countingUOM
      );
    } else if (pkg.toUnit !== stock.countingUOM) {
      rawOpenUnits = convertOpenQty(
        rawOpenUnits,
        pkg.toUnit,
        stock.countingUOM
      );
    }
    group.openQty += rawOpenUnits;
    group.openCost += rawOpenCost;
  }

  const results = [];
  const meta = {
    workArea: {},
  };

  for (const key in items) {
    const group = items[key];

    // -------- PACKAGE ROWS --------
    for (const pkgName in group.packages) {
      const pkgRow = group.packages[pkgName];

      // sample data
      // pkgRow.workAreas = {
      //   1: { name: "Main Store", id: 1, qty: 6, totalCost: 1000 },
      //   2: { name: "Bar", id: 2, qty: 4, totalCost: 800 },
      //   3: { name: "Kitchen", id: 3, qty: 2, totalCost: 600 },
      // };

      const obj = {
        ...group.meta,
        entryType: "Package",
        packageName: pkgName,
        qty: pkgRow.qty,
        totalCost: paiseToRupee(pkgRow.totalCost),
        avgCost: paiseToRupee(
          pkgRow.qty > 0 ? pkgRow.totalCost / pkgRow.qty : 0
        ),
      };
      // Object.keys(pkgRow.workAreas || []).forEach((key) => {
      //   const workArea = pkgRow.workAreas[key];
      //   obj[`workArea_${workArea.id}_qty`] = workArea.qty;
      //   obj[`workArea_${workArea.id}_cost`] = paiseToRupee(workArea.totalCost);
      //   meta.workArea[`${workArea.id}`] = `${workArea.name}`;
      // });
      results.push(obj);
    }

    results.push({
      ...group.meta,
      entryType: "Open",
      packageName: group.meta.uom,
      qty: group.openQty,
      totalCost: paiseToRupee(group.openCost),
      avgCost: paiseToRupee(
        group.openQty > 0 ? group.openCost / group.openQty : 0
      ),
    });
  }

  return { results, meta };
};

// < --------------------start------------------------------->
/**
 * Unit conversion helper
 * Converts value from one unit to another
 */
// function convertToUnit(value, fromUnit, toUnit) {
//   // Conversion map for common units
//   const conversions = {
//     l_ml: 1000,
//     ml_l: 0.001,
//     kg_g: 1000,
//     g_kg: 0.001,
//     l_l: 1,
//     ml_ml: 1,
//     g_g: 1,
//     kg_kg: 1,
//   };

//   if (fromUnit === toUnit) return value;

//   const key = `${fromUnit}_${toUnit}`;
//   const conversionFactor = conversions[key];

//   if (!conversionFactor) {
//     console.warn(
//       `No conversion available from ${fromUnit} to ${toUnit}, returning original value`
//     );
//     return value;
//   }

//   return value * conversionFactor;
// }

/**
 * Combines aggregated stocks by itemId and pkg.id,
 * pivoting inventoryLocationId data into dynamic columns
 */
// function combineStocksByLocation(aggregatedStocks) {
//   // Filter out "Open" entries as they need separate handling
//   const packageEntries = aggregatedStocks.filter(
//     (stock) => stock.entryType === "Package"
//   );
//   const openEntries = aggregatedStocks.filter(
//     (stock) => stock.entryType === "Open"
//   );

//   // Group by itemId + pkg.id
//   const grouped = packageEntries.reduce((acc, stock) => {
//     const key = `${stock.itemId}_${stock.pkg?.id || "default"}`;

//     if (!acc[key]) {
//       acc[key] = {
//         itemId: stock.itemId,
//         itemName: stock.itemName,
//         itemCode: stock.itemCode,
//         categoryName: stock.categoryName,
//         subcategoryName: stock.subcategoryName,
//         pkgId: stock.pkg?.id || "default",
//         pkgName: stock.pkg?.name || "default",
//         pkgQuantity: stock.pkg?.quantity || 1,
//         pkgToUnit: stock.pkg?.toUnit || stock.recipeUOM,
//         countingUOM: stock.countingUOM,
//         recipeUOM: stock.recipeUOM,
//         purchaseUOM: stock.purchaseUOM,
//         entryType: "Package",
//         locations: [],
//         locationName: stock.locationName,
//         totalFullPkg: 0,
//         totalOpenPkg: 0,
//       };
//     }

//     // Add location-specific data as dynamic keys
//     const invLocId = stock.inventoryLocationId;
//     acc[key]["totalFullPkg"] += stock.fullPkgQty;
//     acc[key][`${invLocId}_fullPkgQty`] = stock.fullPkgQty;
//     acc[key][`${invLocId}_openPkgQty`] = stock.openPkgQty;
//     acc[key][`${invLocId}_avgCost`] = stock.avgCost;
//     acc[key][`${invLocId}_totalValue`] = stock.totalValue;
//     acc[key][`${invLocId}_inventoryLocationName`] = stock.inventoryLocationName;

//     // Track locations for reference
//     acc[key].locations.push(invLocId);

//     return acc;
//   }, {});

//   // Convert to array
//   const result = Object.values(grouped);

//   // Handle Open entries similarly - group by itemId only
//   const groupedOpen = openEntries.reduce((acc, stock) => {
//     const key = stock.itemId;

//     if (!acc[key]) {
//       acc[key] = {
//         itemId: stock.itemId,
//         itemName: stock.itemName,
//         itemCode: stock.itemCode,
//         categoryName: stock.categoryName,
//         subcategoryName: stock.subcategoryName,
//         countingUOM: stock.countingUOM,
//         recipeUOM: stock.recipeUOM,
//         purchaseUOM: stock.purchaseUOM,
//         entryType: "Open",
//         locations: [],
//         locationName: stock.locationName,
//         pkgName: stock.purchaseUOM,
//         totalFullPkg: 0,
//         totalOpenPkg: 0,
//       };
//     }

//     const invLocId = stock.inventoryLocationId;
//     acc[key]["totalOpenPkg"] += stock.openPkgQty;
//     acc[key][`${invLocId}_fullPkgQty`] = stock.fullPkgQty;
//     acc[key][`${invLocId}_openPkgQty`] = stock.openPkgQty;
//     acc[key][`${invLocId}_avgCost`] = stock.avgCost;
//     acc[key][`${invLocId}_totalValue`] = stock.totalValue;
//     acc[key][`${invLocId}_inventoryLocationName`] = stock.inventoryLocationName;

//     acc[key].locations.push(invLocId);

//     return acc;
//   }, {});

//   const openResult = Object.values(groupedOpen);

//   return [...result, ...openResult];
// }

/**
 * Aggregates stocks by grouping items and calculating package quantities
 * Automatically combines results by location before returning
 */
// exports.aggregateStocks = async function (
//   tenantId,
//   filters,
//   aggregatorType = ""
// ) {
//   const stocks = await StockRepo.getStocks(tenantId, filters);

//   if (!stocks.length) return [];

//   // Group stocks by itemId
//   const groupedByItem = stocks.reduce((acc, stock) => {
//     const itemId = stock.itemId;
//     if (!acc[itemId]) {
//       acc[itemId] = [];
//     }
//     acc[itemId].push(stock);
//     return acc;
//   }, {});

//   const aggregatedResult = [];

//   // Process each group
//   for (const itemId in groupedByItem) {
//     const group = groupedByItem[itemId];
//     let totalOpenPkgQty = 0;

//     // Process each stock item in the group
//     for (const stock of group) {
//       const recipeQty = stock.recipeQty || 0;
//       const pkgQuantity = stock.pkg?.quantity || 1;
//       const pkgToUnit = stock.pkg?.toUnit || stock.recipeUOM;
//       const recipeUOM = stock.recipeUOM;

//       // Normalize pkg.quantity to recipeUOM for accurate division
//       const normalizedPkgQty = convertToUnit(pkgQuantity, pkgToUnit, recipeUOM);

//       // Calculate full packages (quotient) and open quantity (remainder)
//       const fullPkgQty = Math.floor(recipeQty / normalizedPkgQty);
//       const openPkgQty = recipeQty % normalizedPkgQty;

//       // Accumulate total open quantity for the group
//       totalOpenPkgQty += openPkgQty;

//       // Create package entry with calculated quantities
//       aggregatedResult.push({
//         ...stock,
//         fullPkgQty,
//         openPkgQty, // In countingUOM (same as recipeUOM in most cases)
//         entryType: "Package",
//       });
//     }

//     // Create aggregated "Open" entry for the entire group
//     const templateStock = group[0]; // Use first stock item as template
//     aggregatedResult.push({
//       ...templateStock,
//       id: `${templateStock.groupKey}_open`,
//       fullPkgQty: 0,
//       openPkgQty: totalOpenPkgQty,
//       entryType: "Open",
//     });
//   }

//   console.log(aggregatedResult, "Aggregated Result");
//   // Combine by location before returning
//   const combinedResult = combineStocksByLocation(aggregatedResult);

//   return combinedResult;
// };

// < ------------------------------end --------------------------->

exports.getLedgers = async function (tenantId, filters) {
  const stockLedgers = await StockLedgerRepo.getStockLedgers(tenantId, filters);
  if (stockLedgers.length === 0) return [];

  const results = [];

  for (const stock of stockLedgers) {
    results.push({
      itemId: stock.itemId,
      itemName: stock.itemName,
      itemCode: stock.itemCode,
      inventoryLocationName: stock.inventoryLocationName || null,
      locationName: stock.locationName || null,
      qty: stock.qty,
      uom: stock.pkgUOM,
      totalCost: paiseToRupee(stock.totalCost),
      avgCost: paiseToRupee(0),
      type: stock.ledgerType,
      createdAt: FD.toFormattedDate(stock.createdAt),
      remarks: stock.remarks || stock.reasonCode,
    });
  }

  return results;
};
