// services/stockTransactionService.js
const admin = require("firebase-admin");
const db = admin.firestore();
const grnSchema = require("@/schema/grnSchema");

const { LedgerTypes, StockLedgerReasonCode } = require("@/defs/ledgerDefs");
const { getNextGrnId } = require("./counterService");
const { creditStock, debitStock } = require("./stockService");
const StockLedgerRepo = require("@/repositories/stockLedgerRepo");
const { StockTransactionType } = require("@/defs/ledgerDefs");
const StockRepo = require("@/repositories/stockRepo");
const cartHelper = require("@/helpers/cartHelper");

const {
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const { rupeeToPaise } = require("@/utils/money");
const { handleValidation } = require("@/utils/validation");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const { purchaseStatus } = require("@/defs/purchaseStatusDefs");
const { applyLedgersToDailyStock } = require("./stockMovementsService");

/**
 * =======================
 * IN Operations (Credit)
 * =======================
 */

/**
 * Create a new GRN (Goods Received Note)
 * - Validates items against PO
 * - Updates PO items receivedQty & notes
 * - Persists GRN in Firestore
 * - Calls creditStock for each item to update stock & ledger
 * - Returns the full GRN document
 * - Stores only summary information in GRN (items removed since ledger tracks details)
 */
async function createGRN(
  {
    poData,
    grnDate,
    invoiceDate,
    invoiceNumber,
    grnItems,
    receivedById,
    receivedByName,
    paymentTerms,
    poTerms,
    remarks,
    totalAmount: grnTotalValue,
    totalCess,
    totalChargeAmount,
    totalDiscount,
    totalFocAmount,
    totalTaxAmount,
    netAmount,
    grossAmount,
    taxes,
    charges,
  },
  transaction
) {
  // Validate GRN items against PO and update PO items
  const validItems = grnItems.map((grnItem) => {
    let poItem = poData.items.find((i) => {
      if (i.pkg?.id) {
        return i.itemId === grnItem.itemId && i.pkg.id === grnItem.pkg.id;
      }
      return i.itemId === grnItem.itemId;
    });
    if (!poItem) {
      poItem = grnItem;
      poItem.receivedQty = grnItem.receivedQty;
    } else {
      poItem.receivedQty = (poItem.receivedQty || 0) + grnItem.receivedQty;
    }

    return {
      ordinal: grnItem.ordinal,
      itemId: poItem.itemId,
      itemCode: poItem.itemCode,
      itemName: poItem.itemName,
      orderedQty: poItem.quantity,
      receivedQty: grnItem.receivedQty,
      directIssueList: grnItem.directIssueList,
      unitCost: rupeeToPaise(grnItem.unitCost),
      purchaseUOM: grnItem.purchaseUOM,
      expiryDate: grnItem.expiryDate || new Date(), // @validate date
      remarks: grnItem.remarks || "",
      categoryId: poItem.categoryId,
      subcategoryId: poItem.subcategoryId,
      categoryName: poItem.categoryName,
      subcategoryName: poItem.subcategoryName,
      totalAmount: rupeeToPaise(grnItem.totalAmount),
      totalCess: rupeeToPaise(grnItem.totalCess),
      totalChargeAmount: rupeeToPaise(grnItem.totalChargeAmount),
      totalDiscount: rupeeToPaise(grnItem.totalDiscount),
      totalFocAmount: rupeeToPaise(grnItem.totalFocAmount),
      totalTaxAmount: rupeeToPaise(grnItem.totalTaxAmount),
      netAmount: rupeeToPaise(grnItem.netAmount),
      grossAmount: rupeeToPaise(grnItem.grossAmount),
      stockable: grnItem.stockable,
      taxes: grnItem.taxes,
      charges: grnItem.charges,
      taxRate: grnItem.taxRate,
      pkg: {
        ...grnItem.pkg,
        unitCost: grnItem.pkg.unitCost
          ? rupeeToPaise(grnItem.pkg.unitCost)
          : rupeeToPaise(grnItem.unitCost),
      },
      remarks: poItem.remarks,
      hsnCode: poItem.hsnCode,
      foc: grnItem.foc,
    };
  });

  // Generate GRN ID using counter service
  const grnNumber = await getNextGrnId(poData.tenantId);

  if (poData.transferId) {
    const transferRef = db
      .collection(COLLECTIONS.TRANSFERS)
      .doc(poData.transferId);

    const transferSnap = await transaction.get(transferRef);

    if (!transferSnap.exists) return;

    const transferData = transferSnap.data();
    const existingGrnNumbers = transferData.grnNumbers || [];

    // Prevent duplicates
    if (!existingGrnNumbers.includes(grnNumber)) {
      existingGrnNumbers.push(grnNumber);
    }

    transaction.update(transferRef, {
      grnNumbers: existingGrnNumbers,
    });
  }

  // Form GRN summary (items removed, ledger contains detailed item movements)
  const grnRef = db.collection(COLLECTIONS.GRN).doc(); // @todo: move to repo

  const data = {
    tenantId: poData.tenantId,
    grnNumber,
    grnDate: FD.toFirestore(grnDate, TIME_OPTION.START),
    invoiceDate: FD.toFirestore(invoiceDate, TIME_OPTION.START),
    invoiceNumber,
    id: grnRef.id,
    poId: poData.poId,
    poNumber: poData.poNumber,
    vendorId: poData.vendor.id,
    vendorName: poData.vendor.name,
    vendor: { ...poData.vendor, paymentTerms, poTerms },
    location: {
      id: poData.location.id,
      name: poData.location.name,
    },
    inventoryLocation: {
      id: poData.inventoryLocation.id,
      name: poData.inventoryLocation.name,
    },
    remarks: remarks || null,
    createdBy: {
      id: receivedById,
      name: receivedByName,
    },
    createdAt: FD.now(),
    updatedAt: FD.now(),
    items: validItems,
    status: purchaseStatus.COMPLETED,
    statusTimeline: [
      {
        name: purchaseStatus.COMPLETED,
        time: FD.now(),
        by: {
          name: receivedByName,
          id: receivedById,
        },
      },
    ],
    totalAmount: rupeeToPaise(grnTotalValue),
    totalChargeAmount: rupeeToPaise(totalChargeAmount),
    netAmount: rupeeToPaise(netAmount),
    grossAmount: rupeeToPaise(grossAmount),
    totalFocAmount: rupeeToPaise(totalFocAmount),
    totalTaxAmount: rupeeToPaise(totalTaxAmount),
    totalDiscount: rupeeToPaise(totalDiscount),
    totalCess: rupeeToPaise(totalCess),
    taxes,
    charges,
    transferNumber: poData.transferNumber || null,
  };

  const grn = handleValidation(data, grnSchema);
  if (!grn) return;

  // Save GRN summary in Firestore
  await transaction.set(grnRef, grn);

  // Prepare GRN meta object once
  const grnMeta = {
    id: grnRef.id,
    grnNumber,
    poId: poData.poId,
    vendorId: poData.vendor.id,
    vendorName: poData.vendor.name,
    grnDate: grn.grnDate,
  };

  const poInfo = {
    tenantId: poData.tenantId,
    locationId: poData.location.id,
    locationName: poData.location.name,
    inventoryLocationId: poData.inventoryLocation?.id,
    inventoryLocationName: poData.inventoryLocation?.name,
  };

  // Process each valid item and call creditStock

  const ledgers = [];
  for (const item of validItems) {
    let quantity = item.receivedQty;

    const uom =
      item.pkg && item.pkg.id !== "default" ? item.pkg.name : item.purchaseUOM;

    const ledger = await creditStock(
      {
        ledgerType: LedgerTypes.GRN,
        ...poInfo,
        itemId: item.itemId,
        itemCode: item.itemCode,
        itemName: item.itemName,
        categoryId: item.categoryId,
        subcategoryId: item.subcategoryId,
        categoryName: item.categoryName,
        subcategoryName: item.subcategoryName,
        orderedQty: item.orderedQty,
        qty: quantity,
        pkgUOM: uom,
        unitCost: item.unitCost,
        totalCost: item.totalAmount,
        expiryDate: item.expiryDate,
        discount: item.totalDiscount,
        taxRate: item.taxRate,
        taxAmount: item.totalTaxAmount,
        grnMeta,
        pkg: item.pkg,
        remarks: item.remarks,
        foc: item.foc,
        cess: item.totalCess,
        eventDate: grn.grnDate,
      },
      transaction
    );

    ledgers.push(ledger);
  }

  applyLedgersToDailyStock({
    ...poInfo,
    eventDate: data.grnDate,
    ledgers: ledgers,
  });

  return {
    grn,
    updatedPO: poData,
  };
}

async function returnVendor({
  grnId,
  tenantId,
  payload,
  removedById,
  removedByName,
  ledgerType,
}) {
  return db.runTransaction(async (t) => {
    const grnRef = db.collection(COLLECTIONS.GRN).doc(grnId);
    const grnSnap = await t.get(grnRef);

    if (!grnSnap.exists) throw new Error("GRN not found");

    const grn = grnSnap.data();

    if (grn.status === purchaseStatus.DELETED)
      throw new Error("GRN already deleted");

    if (grn.status === purchaseStatus.RETURN_VENDOR)
      throw new Error("GRN already returned to vendor");

    // Unique key builder
    const makeKey = (item) =>
      `${item.itemId}__${item.pkg?.id || "default"}__${item.ordinal}`;

    // Filter only rtvQty > 0
    const returnItems = payload.items.filter((i) => i.rtvQty > 0);

    // Map returnItems using unique key
    const returnMap = new Map(returnItems.map((i) => [makeKey(i), i]));

    // ------------------------------------------------------------
    // STOCK DEBIT
    // ------------------------------------------------------------
    for (const item of grn.items) {
      const r = returnMap.get(makeKey(item));
      if (!r) continue;

      const qty = r.rtvQty;

      await debitStock({
        ledgerType,
        tenantId,
        locationId: grn.location.id,
        locationName: grn.location.name,
        inventoryLocationId: grn.inventoryLocation.id,
        inventoryLocationName: grn.inventoryLocation.name,
        itemId: item.itemId,
        itemCode: item.itemCode,
        itemName: item.itemName,
        qty,
        pkgUOM: item.pkg?.id !== "default" ? item.pkg.name : item.purchaseUOM,
        unitCost: rupeeToPaise(item.unitCost),
        totalCost: rupeeToPaise(item.unitCost * qty),
        grnMeta: null,
        pkg: item.pkg,
        remarks: payload.remarks || null,
        eventDate: grn.grnDate,
      });
    }

    // ------------------------------------------------------------
    // RECALCULATE GRN ITEMS (cartHelper per line)
    // ------------------------------------------------------------
    const updatedItems = [];

    for (const item of grn.items) {
      const r = returnMap.get(makeKey(item));

      if (!r) {
        // No return for this line
        updatedItems.push(item);
        continue;
      }

      const remainingQty = item.receivedQty - r.rtvQty;

      if (remainingQty < 0)
        throw new Error(`Returned qty exceeds GRN qty for ${item.itemName}`);

      // Build a small cart to recalculate item amounts
      const cart = cartHelper.NewCart("pr");
      const cartItem = cartHelper.NewCartItem();

      Object.assign(cartItem, item, {
        receivedQty: remainingQty,
        quantity: remainingQty,
        rtvQty: 0,
      });

      const recalculated = cartHelper.CalculateItem(cart, cartItem);

      updatedItems.push({
        ...item,
        ...recalculated,
        receivedQty: remainingQty,
      });
    }

    // ------------------------------------------------------------
    // RECALCULATE GRN TOTALS USING cartHelper.Calculate
    // ------------------------------------------------------------
    const grnCart = cartHelper.NewCart("pr");
    grnCart.items = updatedItems.map((i) => ({ ...i }));

    const recalculatedTotals = cartHelper.Calculate(grnCart);

    // ------------------------------------------------------------
    // BUILD RETURN INFO OBJECT
    // ------------------------------------------------------------
    const returnInfo = {
      creditNoteNo: payload.creditNoteNo || null,
      creditNoteDate: payload.creditNoteDate || null,
      returnDate: payload.returnDate || null,
      remarks: payload.remarks || null,
      items: returnItems.map((i) => ({
        itemId: i.itemId,
        itemName: i.itemName,
        itemCode: i.itemCode,
        ordinal: i.ordinal,
        pkg: i.pkg,
        rtvQty: i.rtvQty,
        unitCost: i.unitCost,
        totalAmount: i.totalAmount,
        remarks: i.remarks,
      })),
    };

    // ------------------------------------------------------------
    // UPDATE GRN DOCUMENT
    // ------------------------------------------------------------
    t.update(grnRef, {
      status:
        ledgerType === LedgerTypes.GRN_DELETE
          ? purchaseStatus.DELETED
          : purchaseStatus.RETURN_VENDOR,

      updatedAt: FD.now(),

      returnInfo,

      // Updated GRN line items
      items: updatedItems,

      // Updated totals from cartHelper
      grossAmount: recalculatedTotals.grossAmount,
      totalDiscount: recalculatedTotals.totalDiscount,
      netAmount: recalculatedTotals.netAmount,
      totalChargeAmount: recalculatedTotals.totalChargeAmount,
      totalTaxAmount: recalculatedTotals.totalTaxAmount,
      totalAmount:
        recalculatedTotals.totalAmount -
        recalculatedTotals.totalFocAmount +
        recalculatedTotals.totalChargeAmount,
      totalCess: recalculatedTotals.totalCess,
      totalFocAmount: recalculatedTotals.totalFocAmount,
      taxes: recalculatedTotals.taxes,

      statusTimeline: [
        ...grn.statusTimeline,
        {
          name:
            ledgerType === LedgerTypes.GRN_DELETE
              ? purchaseStatus.DELETED
              : purchaseStatus.RETURN_VENDOR,
          time: FD.now(),
          by: { id: removedById, name: removedByName },
        },
      ],
    });

    return {
      grnId,
      message:
        ledgerType === LedgerTypes.GRN_DELETE
          ? "GRN deleted successfully"
          : "GRN returned to vendor successfully",
    };
  });
}

async function deleteGRN({
  grnId,
  tenantId,
  reason,
  removedById,
  removedByName,
  ledgerType,
  ledgerReasonCode,
}) {
  return db.runTransaction(async (t) => {
    const grnRef = db.collection(COLLECTIONS.GRN).doc(grnId);
    const grnSnap = await t.get(grnRef);

    if (!grnSnap.exists) throw new Error("GRN not found");

    const grn = grnSnap.data();

    if (grn.status === purchaseStatus.DELETED)
      throw new Error("GRN already deleted");

    if (grn.status === purchaseStatus.RETURN_VENDOR)
      throw new Error("GRN already returned to vendor");

    // -------- Phase 1: Validate and cache ledgers --------
    const invalidItems = [];
    const ledgerCache = [];

    for (const item of grn.items) {
      const snap = await db
        .collection(COLLECTIONS.LEDGERS)
        .where("tenantId", "==", tenantId)
        .where("ledgerType", "==", LedgerTypes.GRN)
        .where("grnMeta.id", "==", grnId)
        .where("itemId", "==", item.itemId)
        .where("pkg.id", "==", item.pkg.id)
        .orderBy("createdAt", "desc")
        .limit(1)
        .get();

      if (snap.empty)
        throw new Error(`No ledger found for GRN item: ${item.itemName}`);

      const doc = snap.docs[0];
      const data = doc.data();

      if (data.remainingQty < item.receivedQty) {
        invalidItems.push(item.itemName);
      }

      ledgerCache.push({
        item,
        ledgerRef: doc.ref,
        ledgerData: data,
      });
    }

    if (invalidItems.length > 0) {
      throw new Error(
        `This GRN cannot be deleted because stock is already consumed for: ${invalidItems.join(
          ", "
        )}. Please contact support.`
      );
    }

    // -------- Phase 2: Apply mutations --------
    for (const { item, ledgerRef } of ledgerCache) {
      const qty = item.receivedQty;

      // 1. Zero out original IN ledger
      t.update(ledgerRef, {
        remainingQty: 0,
        updatedAt: FD.now(),
      });

      // 2. Create reversing OUT ledger
      await StockLedgerRepo.addEntry(
        {
          tenantId,
          ledgerType: LedgerTypes.ADJUSTMENT,
          reasonCode: StockLedgerReasonCode.GRN_DELETE,
          transactionType: StockTransactionType.OUT,
          itemId: item.itemId,
          itemCode: item.itemCode,
          itemName: item.itemName,
          categoryId: item.categoryId,
          subcategoryId: item.subcategoryId,
          categoryName: item.categoryName,
          subcategoryName: item.subcategoryName,
          qty,
          remainingQty: 0,
          remainingQtyInRecipeUOM: 0,
          countingUOM: item.pkg?.name || item.purchaseUOM,
          unitCost: item.unitCost,
          totalCost: item.totalAmount,
          expiryDate: item.expiryDate,
          discount: item.totalDiscount,
          taxRate: item.taxRate,
          taxAmount: item.totalTaxAmount,
          pkg: item.pkg,
          pkgUOM: item.pkg.name,
          grnMeta: {
            id: grnId,
            grnNumber: grn.grnNumber,
            reversed: true,
          },
          locationId: grn.location.id,
          locationName: grn.location.name,
          inventoryLocationId: grn.inventoryLocation.id,
          inventoryLocationName: grn.inventoryLocation.name,
        },
        t
      );

      // 3. Reduce stock
      await StockRepo.decreaseStock(
        {
          itemId: item.itemId,
          inventoryLocationId: grn.inventoryLocation.id,
          packageId: item.pkg?.id,
          qty,
          recipeQty: 0,
          totalValue: item.totalAmount,
        },
        t
      );
    }

    // -------- Final GRN update --------
    let updateStatus;
    let updateMessage;

    if (
      ledgerType === LedgerTypes.ADJUSTMENT &&
      ledgerReasonCode === StockLedgerReasonCode.GRN_DELETE
    ) {
      updateStatus = purchaseStatus.DELETED;
      updateMessage = "GRN deleted successfully";
    } else {
      updateStatus = purchaseStatus.RETURN_VENDOR;
      updateMessage = "GRN returned to vendor successfully";
    }

    t.update(grnRef, {
      status: updateStatus,
      removedAt: FD.now(),
      updatedAt: FD.now(),
      removedBy: {
        id: removedById,
        name: removedByName,
      },
      reason: reason || null,
      statusTimeline: [
        ...grn.statusTimeline,
        {
          name: updateStatus,
          time: FD.now(),
          by: { id: removedById, name: removedByName },
        },
      ],
    });

    return { grnId, message: updateMessage };
  });
}

module.exports = {
  createGRN,
  deleteGRN,
  returnVendor,
};
